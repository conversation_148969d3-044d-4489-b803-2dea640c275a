{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "declaration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "composite": true, "baseUrl": "."}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../shared"}]}