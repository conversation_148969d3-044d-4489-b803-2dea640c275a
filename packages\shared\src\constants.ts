// API endpoints
export const API_ENDPOINTS = {
  USERS: '/api/users',
  TRIPS: '/api/trips',
  AUTH: '/api/auth',
  UPLOAD: '/api/upload',
} as const;

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Error codes
export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
} as const;

// Default pagination
export const DEFAULT_PAGINATION = {
  page: 1,
  limit: 10,
  maxLimit: 100,
} as const;

// Date formats
export const DATE_FORMATS = {
  ISO: 'YYYY-MM-DDTHH:mm:ssZ',
  SHORT: 'YYYY-MM-DD',
  LONG: 'MMMM DD, YYYY',
} as const;

// App configuration
export const APP_CONFIG = {
  NAME: 'TravelViz',
  VERSION: '1.0.0',
  DESCRIPTION: 'AI-powered travel planning platform',
} as const; 