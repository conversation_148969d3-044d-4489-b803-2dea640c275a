"use client";

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Send, Bot, User, MapPin, Calendar, Clock, DollarSign, Sparkles, Copy, RefreshCw, Save, Menu, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const suggestedPrompts = [
  "Plan a 5-day trip to Tokyo for first-time visitors",
  "Create a romantic weekend in Paris",
  "Design a family-friendly California road trip",
  "Plan a budget backpacking trip through Southeast Asia",
  "Organize a food tour of Italy for 10 days",
  "Create a winter adventure in Iceland"
];

type ItineraryItem = {
  time: string;
  title: string;
  location: string;
  category: string;
  duration: number;
  cost: number;
};

type ItineraryDay = {
  day: number;
  date: string;
  theme: string;
  items: ItineraryItem[];
};

type Itinerary = {
  title: string;
  description: string;
  duration: number;
  estimatedCost: number;
  days: ItineraryDay[];
};

const mockItinerary: Itinerary = {
  title: "7 Days in Tokyo & Kyoto",
  description: "A perfect blend of modern Tokyo and traditional Kyoto",
  duration: 7,
  estimatedCost: 1200,
  days: [
    {
      day: 1,
      date: "2024-04-15",
      theme: "Tokyo Arrival & Shibuya",
      items: [
        {
          time: "14:00",
          title: "Arrive at Haneda Airport",
          location: "Haneda Airport, Tokyo",
          category: "transport",
          duration: 60,
          cost: 0
        },
        {
          time: "16:00",
          title: "Check into Hotel in Shibuya",
          location: "Shibuya District, Tokyo",
          category: "accommodation",
          duration: 30,
          cost: 120
        },
        {
          time: "18:00",
          title: "Shibuya Crossing Experience",
          location: "Shibuya Crossing, Tokyo",
          category: "activity",
          duration: 90,
          cost: 0
        }
      ]
    }
  ]
};

export default function AIChatPage() {
  const [messages, setMessages] = useState([
    {
      id: 1,
      type: 'bot',
      content: "Hi! I'm your AI travel planning assistant. Tell me about your dream trip and I'll create a detailed visual itinerary for you. Where would you like to go?",
      timestamp: new Date()
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [currentItinerary, setCurrentItinerary] = useState<Itinerary | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isMobilePreviewOpen, setIsMobilePreviewOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    setIsGenerating(true);

    // Simulate AI response
    setTimeout(() => {
      setIsTyping(false);
      const botMessage = {
        id: Date.now() + 1,
        type: 'bot',
        content: "Perfect! I'm creating a detailed itinerary for your Tokyo and Kyoto adventure. This will include the best temples, restaurants, and cultural experiences.",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botMessage]);
      
      // Simulate itinerary generation
      setTimeout(() => {
        setCurrentItinerary(mockItinerary);
        setIsGenerating(false);
        if (isMobile) {
          setIsMobilePreviewOpen(true);
        }
      }, 2000);
    }, 1500);
  };

  const handlePromptClick = (prompt: string) => {
    setInputValue(prompt);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content);
  };

  const regenerateResponse = () => {
    setIsTyping(true);
    setTimeout(() => {
      setIsTyping(false);
      const botMessage = {
        id: Date.now(),
        type: 'bot',
        content: "Let me create an alternative itinerary with different activities and experiences for your trip.",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, botMessage]);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-500 rounded-lg">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg lg:text-xl font-bold text-gray-900">AI Travel Assistant</h1>
                <p className="text-xs lg:text-sm text-gray-600">Create your perfect itinerary with AI</p>
              </div>
            </div>
            <div className="flex items-center space-x-2 lg:space-x-4">
              <Badge variant="outline" className="text-xs">
                7/10 AI generations used
              </Badge>
              {isMobile && currentItinerary && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setIsMobilePreviewOpen(true)}
                >
                  <MapPin className="h-4 w-4 mr-1" />
                  Preview
                </Button>
              )}
              <Button variant="outline" size="sm" className="hidden lg:inline-flex">
                View Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-8 h-[calc(100vh-8rem)] lg:h-[calc(100vh-12rem)]">
          {/* Chat Panel */}
          <div className="bg-white rounded-xl shadow-sm flex flex-col">
            <div className="p-4 lg:p-6 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Chat with AI</h2>
              <p className="text-sm text-gray-600">Describe your dream trip and get instant itineraries</p>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 lg:p-6 space-y-4">
              {messages.map((message) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-[85%] lg:max-w-[80%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
                    <div className={`flex items-start space-x-3 ${message.type === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        message.type === 'user' 
                          ? 'bg-orange-500 text-white' 
                          : 'bg-blue-500 text-white'
                      }`}>
                        {message.type === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                      </div>
                      <div className={`rounded-lg p-3 lg:p-4 ${
                        message.type === 'user'
                          ? 'bg-orange-500 text-white'
                          : 'bg-gray-100 text-gray-900'
                      }`}>
                        <p className="text-sm">{message.content}</p>
                        <div className="flex items-center justify-between mt-2">
                          <span className={`text-xs ${
                            message.type === 'user' ? 'text-orange-100' : 'text-gray-500'
                          }`}>
                            {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                          {message.type === 'bot' && (
                            <div className="flex items-center space-x-1">
                              <button
                                onClick={() => copyMessage(message.content)}
                                className="text-gray-400 hover:text-gray-600"
                              >
                                <Copy className="h-3 w-3" />
                              </button>
                              <button
                                onClick={regenerateResponse}
                                className="text-gray-400 hover:text-gray-600"
                              >
                                <RefreshCw className="h-3 w-3" />
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}

              {/* Typing Indicator */}
              <AnimatePresence>
                {isTyping && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="flex justify-start"
                  >
                    <div className="flex items-start space-x-3">
                      <div className="w-8 h-8 rounded-full bg-blue-500 text-white flex items-center justify-center">
                        <Bot className="h-4 w-4" />
                      </div>
                      <div className="bg-gray-100 rounded-lg p-4">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-100"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce delay-200"></div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>

              <div ref={messagesEndRef} />
            </div>

            {/* Suggested Prompts */}
            {messages.length === 1 && (
              <div className="p-4 lg:p-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-3">Try these suggestions:</h3>
                <div className="flex flex-wrap gap-2">
                  {suggestedPrompts.map((prompt, index) => (
                    <button
                      key={index}
                      onClick={() => handlePromptClick(prompt)}
                      className="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-full transition-colors"
                    >
                      {prompt}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Input */}
            <div className="p-4 lg:p-6 border-t border-gray-200">
              <div className="flex space-x-2 lg:space-x-4">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Describe your dream trip..."
                  className="flex-1"
                />
                <Button onClick={handleSendMessage} className="btn-primary">
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Preview Panel - Desktop */}
          <div className="hidden lg:flex bg-white rounded-xl shadow-sm flex-col">
            <PreviewContent />
          </div>

          {/* Mobile Preview Modal */}
          <AnimatePresence>
            {isMobilePreviewOpen && (
              <>
                {/* Backdrop */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="fixed inset-0 bg-black/50 z-50 lg:hidden"
                  onClick={() => setIsMobilePreviewOpen(false)}
                />
                
                {/* Modal */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  className="fixed inset-4 bg-white rounded-xl shadow-xl z-50 lg:hidden flex flex-col"
                >
                  <div className="flex items-center justify-between p-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-900">Live Preview</h2>
                    <button
                      onClick={() => setIsMobilePreviewOpen(false)}
                      className="p-2 rounded-lg hover:bg-gray-100"
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>
                  <div className="flex-1 overflow-hidden">
                    <PreviewContent />
                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );

  function PreviewContent() {
    return (
      <>
        <div className="p-4 lg:p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">Live Preview</h2>
              <p className="text-sm text-gray-600">Watch your itinerary come to life</p>
            </div>
            {currentItinerary && (
              <Button size="sm" className="btn-primary">
                <Save className="h-4 w-4 mr-2" />
                Save Trip
              </Button>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-4 lg:p-6">
          {isGenerating ? (
            <div className="flex flex-col items-center justify-center h-full space-y-4">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                <Sparkles className="h-8 w-8 text-orange-500 animate-pulse" />
              </div>
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Creating Your Itinerary</h3>
                <p className="text-gray-600">AI is analyzing destinations and crafting the perfect plan...</p>
              </div>
              <div className="w-full max-w-xs">
                <div className="bg-gray-200 rounded-full h-2">
                  <div className="bg-orange-500 h-2 rounded-full animate-pulse" style={{ width: '60%' }}></div>
                </div>
              </div>
            </div>
          ) : currentItinerary ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              {/* Itinerary Header */}
              <div className="text-center">
                <h3 className="text-xl lg:text-2xl font-bold text-gray-900 mb-2">{currentItinerary.title}</h3>
                <p className="text-gray-600 mb-4">{currentItinerary.description}</p>
                
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-10 h-10 lg:w-12 lg:h-12 bg-blue-100 rounded-lg mb-2">
                      <Calendar className="h-5 w-5 lg:h-6 lg:w-6 text-blue-600" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{currentItinerary.duration}</div>
                    <div className="text-sm text-gray-600">Days</div>
                  </div>
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-10 h-10 lg:w-12 lg:h-12 bg-green-100 rounded-lg mb-2">
                      <MapPin className="h-5 w-5 lg:h-6 lg:w-6 text-green-600" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">12</div>
                    <div className="text-sm text-gray-600">Locations</div>
                  </div>
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center w-10 h-10 lg:w-12 lg:h-12 bg-orange-100 rounded-lg mb-2">
                      <DollarSign className="h-5 w-5 lg:h-6 lg:w-6 text-orange-600" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">${currentItinerary.estimatedCost}</div>
                    <div className="text-sm text-gray-600">Budget</div>
                  </div>
                </div>
              </div>

              {/* Daily Timeline */}
              <div className="space-y-4">
                {currentItinerary.days.map((day: any, dayIndex: number) => (
                  <Card key={dayIndex} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-semibold text-gray-900">Day {day.day}</h4>
                        <p className="text-sm text-gray-600">{day.theme}</p>
                      </div>
                      <Badge variant="outline">{day.date}</Badge>
                    </div>
                    
                    <div className="space-y-3">
                      {day.items.map((item: any, itemIndex: number) => (
                        <div key={itemIndex} className="flex items-start space-x-3">
                          <div className="flex-shrink-0">
                            <div className={`w-6 h-6 lg:w-8 lg:h-8 rounded-full flex items-center justify-center text-white text-xs font-bold ${
                              item.category === 'activity' ? 'bg-blue-500' :
                              item.category === 'restaurant' ? 'bg-red-500' :
                              item.category === 'accommodation' ? 'bg-green-500' :
                              'bg-purple-500'
                            }`}>
                              {itemIndex + 1}
                            </div>
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <h5 className="text-sm font-medium text-gray-900 truncate">{item.title}</h5>
                              <span className="text-xs text-gray-500">${item.cost}</span>
                            </div>
                            <div className="flex items-center space-x-2 text-xs text-gray-500 mt-1">
                              <Clock className="h-3 w-3" />
                              <span>{item.time}</span>
                              <span>•</span>
                              <span>{item.duration} min</span>
                            </div>
                            <p className="text-xs text-gray-600 mt-1 truncate">{item.location}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>
                ))}
              </div>
            </motion.div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-center space-y-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <MapPin className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Start Planning</h3>
                <p className="text-gray-600">
                  Tell the AI about your travel preferences and watch your itinerary appear here in real-time.
                </p>
              </div>
            </div>
          )}
        </div>
      </>
    );
  }
}