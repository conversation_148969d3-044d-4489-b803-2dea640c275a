"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Menu, X, ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { WaitlistSignup } from '@/components/WaitlistSignup';

const navigationItems = [
  {
    name: 'Features',
    href: '#features',
    description: 'Discover what makes TravelViz special'
  },
  {
    name: 'Pricing',
    href: '#pricing',
    description: 'Simple, transparent pricing'
  },
  {
    name: 'Templates',
    href: '/templates',
    description: 'Ready-made trip templates'
  },
  {
    name: 'Resources',
    href: '#',
    description: 'Help and learning materials',
    submenu: [
      { name: 'About Us', href: '/about', description: 'Learn about our mission' },
      { name: 'Contact', href: '/contact', description: 'Get in touch with us' },
      { name: 'Privacy Policy', href: '/privacy', description: 'How we protect your data' },
      { name: 'Terms of Service', href: '/terms', description: 'Our terms and conditions' }
    ]
  }
];

export function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSubmenu, setActiveSubmenu] = useState<string | null>(null);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu when clicking outside or on a link
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isMobileMenuOpen && !target?.closest('.mobile-menu-container')) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isMobileMenuOpen]);

  // Prevent body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }
    
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  return (
    <>
      <header 
        className={`fixed top-0 w-full z-50 transition-all duration-300 ${
          isScrolled 
            ? 'bg-white/95 backdrop-blur-sm shadow-sm border-b border-gray-200' 
            : 'bg-transparent'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="group-hover:scale-105 transition-transform">
                <Image 
                  src="/logo-small.svg" 
                  alt="TravelViz Logo" 
                  width={40} 
                  height={40}
                  className="w-10 h-10"
                />
              </div>
              <span className="text-xl font-bold text-gray-900">TravelViz</span>
            </Link>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <div
                  key={item.name}
                  className="relative"
                  onMouseEnter={() => item.submenu && setActiveSubmenu(item.name)}
                  onMouseLeave={() => setActiveSubmenu(null)}
                >
                  <Link
                    href={item.href}
                    className="flex items-center space-x-1 px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors font-medium rounded-lg hover:bg-gray-50"
                  >
                    <span>{item.name}</span>
                    {item.submenu && (
                      <ChevronDown className={`h-4 w-4 transition-transform ${
                        activeSubmenu === item.name ? 'rotate-180' : ''
                      }`} />
                    )}
                  </Link>

                  {/* Submenu */}
                  {item.submenu && (
                    <AnimatePresence>
                      {activeSubmenu === item.name && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute top-full left-0 mt-2 w-80 bg-white rounded-xl shadow-lg border border-gray-200 py-2"
                        >
                          {item.submenu.map((subItem) => (
                            <Link
                              key={subItem.name}
                              href={subItem.href}
                              className="block px-4 py-3 hover:bg-gray-50 transition-colors"
                            >
                              <div className="font-medium text-gray-900">{subItem.name}</div>
                              <div className="text-sm text-gray-500">{subItem.description}</div>
                            </Link>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
              ))}
            </nav>

            {/* Desktop Actions */}
            <div className="hidden lg:flex items-center">
              <WaitlistSignup size="sm" className="flex-shrink-0" />
            </div>

            {/* Mobile Menu Button */}
            <div className="mobile-menu-container lg:hidden">
              <button
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors focus:outline-none focus:ring-2 focus:ring-orange-500 min-h-[44px] min-w-[44px] flex items-center justify-center"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                aria-label="Toggle mobile menu"
                aria-expanded={isMobileMenuOpen}
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6 text-gray-600" />
                ) : (
                  <Menu className="h-6 w-6 text-gray-600" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <>
              {/* Backdrop */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 bg-black/50 z-40 lg:hidden"
                onClick={() => setIsMobileMenuOpen(false)}
              />
              
              {/* Menu Panel */}
              <motion.div
                initial={{ opacity: 0, x: '100%' }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: '100%' }}
                transition={{ type: 'tween', duration: 0.3 }}
                className="mobile-menu-container fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-white shadow-xl z-50 lg:hidden overflow-y-auto safe-area-inset-top safe-area-inset-bottom"
              >
                <div className="p-6">
                  {/* Mobile Header */}
                  <div className="flex items-center justify-between mb-8">
                    <Link href="/" className="flex items-center space-x-2" onClick={() => setIsMobileMenuOpen(false)}>
                      <div>
                        <Image 
                          src="/logo-small.svg" 
                          alt="TravelViz Logo" 
                          width={40} 
                          height={40}
                          className="w-10 h-10"
                        />
                      </div>
                      <span className="text-xl font-bold text-gray-900">TravelViz</span>
                    </Link>
                    <button
                      onClick={() => setIsMobileMenuOpen(false)}
                      className="p-2 rounded-lg hover:bg-gray-100 transition-colors min-h-[44px] min-w-[44px] flex items-center justify-center"
                      aria-label="Close menu"
                    >
                      <X className="h-6 w-6 text-gray-600" />
                    </button>
                  </div>

                  {/* Navigation Links */}
                  <nav className="space-y-2 mb-8">
                    {navigationItems.map((item) => (
                      <div key={item.name}>
                        <Link 
                          href={item.href}
                          className="block py-3 px-4 text-gray-700 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors font-medium min-h-[44px] flex items-center"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {item.name}
                        </Link>
                        {item.submenu && (
                          <div className="ml-4 mt-2 space-y-1">
                            {item.submenu.map((subItem) => (
                              <Link
                                key={subItem.name}
                                href={subItem.href}
                                className="block py-2 px-4 text-sm text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors min-h-[40px] flex items-center"
                                onClick={() => setIsMobileMenuOpen(false)}
                              >
                                {subItem.name}
                              </Link>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </nav>

                  {/* Waitlist Signup */}
                  <div className="space-y-3">
                    <WaitlistSignup size="md" showBenefits={true} />
                  </div>

                  {/* Footer Links */}
                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="space-y-3 text-sm">
                      <Link 
                        href="/privacy" 
                        className="block text-gray-600 hover:text-orange-600 transition-colors py-2"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Privacy Policy
                      </Link>
                      <Link 
                        href="/terms" 
                        className="block text-gray-600 hover:text-orange-600 transition-colors py-2"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        Terms of Service
                      </Link>
                    </div>
                  </div>
                </div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </header>

      {/* Skip link for accessibility */}
      <a 
        href="#main-content" 
        className="skip-link focus:not-sr-only"
      >
        Skip to main content
      </a>
    </>
  );
}