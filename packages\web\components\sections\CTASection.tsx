"use client";

import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowR<PERSON>, Sparkles, Users, Clock, Share2, Download, Globe, DollarSign, Bell, TrendingUp, Play } from 'lucide-react';
import { WaitlistSignup } from '@/components/WaitlistSignup';

export function CTASection() {
  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-gradient-to-r from-orange-500 to-pink-500 relative overflow-hidden safe-area-inset-left safe-area-inset-right safe-area-inset-bottom">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-20 h-20 border-2 border-white rounded-full"></div>
        <div className="absolute top-32 right-20 w-16 h-16 border-2 border-white rounded-full"></div>
        <div className="absolute bottom-20 left-32 w-12 h-12 border-2 border-white rounded-full"></div>
        <div className="absolute bottom-32 right-10 w-24 h-24 border-2 border-white rounded-full"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="space-y-6 sm:space-y-8"
        >
          <div className="space-y-3 sm:space-y-4">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white leading-tight px-2 sm:px-0">
              Stop Losing Money on Travel.
              <br />
              <span className="text-yellow-300">Start Saving Today.</span>
            </h2>
            <p className="text-lg sm:text-xl md:text-2xl text-white/90 max-w-3xl mx-auto px-4 sm:px-0">
              Join travelers who save an average of $400 per trip with automatic price alerts, 
              budget tracking, and seamless AI-powered planning.
            </p>
          </div>

          {/* Key Value Props */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3, duration: 0.6 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6 py-6 sm:py-8 max-w-4xl mx-auto"
          >
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6">
              <DollarSign className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-300 mx-auto mb-3" />
              <h3 className="font-semibold text-white mb-2">Save $400+ Per Trip</h3>
              <p className="text-sm text-white/80">Automatic price alerts and budget tracking</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6">
              <Clock className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-300 mx-auto mb-3" />
              <h3 className="font-semibold text-white mb-2">30-Second Planning</h3>
              <p className="text-sm text-white/80">AI conversation to visual itinerary instantly</p>
            </div>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6">
              <Users className="h-6 w-6 sm:h-8 sm:w-8 text-yellow-300 mx-auto mb-3" />
              <h3 className="font-semibold text-white mb-2">Group Coordination</h3>
              <p className="text-sm text-white/80">Real-time collaboration and cost sharing</p>
            </div>
          </motion.div>

          {/* Detailed Benefits */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.5, duration: 0.6 }}
            className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 sm:p-8 max-w-5xl mx-auto"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-white mb-6">
              Everything You Need to Save Money & Plan Better
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
              <div className="space-y-3">
                <h4 className="font-semibold text-yellow-300 flex items-center">
                  <Bell className="h-4 w-4 mr-2" />
                  Smart Money Features
                </h4>
                <ul className="space-y-2 text-white/90 text-sm">
                  <li className="flex items-start">
                    <TrendingUp className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    Automatic price drop alerts for flights & hotels
                  </li>
                  <li className="flex items-start">
                    <DollarSign className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    Budget tracking within your itinerary
                  </li>
                  <li className="flex items-start">
                    <Sparkles className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    Find deals on AI-suggested places
                  </li>
                  <li className="flex items-start">
                    <Share2 className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    Share costs transparently with travel partners
                  </li>
                </ul>
              </div>
              
              <div className="space-y-3">
                <h4 className="font-semibold text-yellow-300 flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  Collaboration & Efficiency
                </h4>
                <ul className="space-y-2 text-white/90 text-sm">
                  <li className="flex items-start">
                    <Globe className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    One link to share entire trip plan
                  </li>
                  <li className="flex items-start">
                    <Users className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    See who has viewed and approved plans
                  </li>
                  <li className="flex items-start">
                    <Share2 className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    Real-time collaborative editing
                  </li>
                  <li className="flex items-start">
                    <Download className="h-3 w-3 mr-2 mt-1 flex-shrink-0" />
                    Full offline access during trips
                  </li>
                </ul>
              </div>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="space-y-4"
          >
            <div className="max-w-lg mx-auto">
              <WaitlistSignup 
                size="lg" 
                variant="secondary"
                showBenefits={false}
                className="px-4 sm:px-0"
              />
            </div>

            <p className="text-white/80 text-xs sm:text-sm px-4 sm:px-0">
              Get early access • Average savings: $400 per trip • Join travelers on the waitlist
            </p>
          </motion.div>

          {/* Social Proof Numbers */}
          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.9, duration: 0.5 }}
            className="mobile-grid sm:flex sm:flex-row sm:items-center sm:justify-center sm:space-y-0 sm:space-x-8 pt-6 sm:pt-8 text-white/70 text-xs sm:text-sm"
          >
            <div className="flex items-center justify-center space-x-2 mb-2 sm:mb-0">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span>Average $400 saved per users</span>
            </div>
            <div className="flex items-center justify-center space-x-2 mb-2 sm:mb-0">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <span>4.9★ average rating by users</span>
            </div>
            <div className="flex items-center justify-center space-x-2 mb-2 sm:mb-0">
              <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
              <span> 100% happy travelers </span>
            </div>
            
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}