"use client";

import Link from 'next/link';
import { ArrowLeft, MapPin, Users, Target, Heart, Zap, Globe, Award, Mail, ExternalLink } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="p-2 bg-orange-500 rounded-lg">
                <MapPin className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">TravelViz</span>
            </Link>
            <Link href="/">
              <Button variant="ghost" className="flex items-center">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white py-16 sm:py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl sm:text-5xl font-bold mb-6">
            Built by Travelers,
            <br />
            <span className="text-yellow-300">For Travelers</span>
          </h1>
          <p className="text-xl sm:text-2xl text-white/90 max-w-3xl mx-auto mb-8">
            As fellow travelers, we understand the challenges of discovering new places and connecting with like-minded adventurers. 
            Our app is built from the ground up with one simple mission: to make travel planning and experiences more accessible, enjoyable, and authentic for everyone.
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-white/80">
            <div className="flex items-center space-x-2">
              <Heart className="h-5 w-5" />
              <span>Genuine passion for exploration</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5" />
              <span>Building our early community</span>
            </div>
            <div className="flex items-center space-x-2">
              <Globe className="h-5 w-5" />
              <span>No fake promises, just authentic travel</span>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16">
        {/* Founder's Story */}
        <section className="mb-16 sm:mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Meet Our Founder</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Every great product starts with a genuine problem that needs solving
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 sm:gap-12 items-center">
            <div className="space-y-6">
              <div className="bg-white rounded-xl p-6 sm:p-8 shadow-sm">
                <div className="flex items-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4">
                    LZ
                  </div>
                  <div>
                    <h3 className="text-xl sm:text-2xl font-bold text-gray-900">Louis Zeng</h3>
                    <p className="text-gray-600">Founder & Lead Developer</p>

                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed mb-4">
                  "I've always been passionate about exploration and connecting with fellow travelers. After countless trips where I struggled with 
                  chaotic planning tools and disconnected travel experiences, I realized there had to be a better way."
                </p>
                <p className="text-gray-700 leading-relaxed">
                  "TravelViz isn't just another travel app - it's my genuine attempt to solve the real problems I've faced as a traveler. 
                  We're building this together with our community, one authentic experience at a time."
                </p>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 sm:p-8 shadow-sm">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-6">Our Authentic Journey</h3>
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                    <span className="text-orange-600 font-bold text-sm">Start</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">The Problem</h4>
                    <p className="text-sm text-gray-600">Frustrated with disconnected travel planning tools and lack of authentic community</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-bold text-sm">Build</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">The Solution</h4>
                    <p className="text-sm text-gray-600">Creating a platform that truly understands travelers' needs and fosters genuine connections</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                    <span className="text-green-600 font-bold text-sm">Grow</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">The Community</h4>
                    <p className="text-sm text-gray-600">Building alongside our early users to shape the future of travel experiences</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Our Values */}
        <section className="mb-16 sm:mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">What We Believe</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The principles that guide everything we build
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
            {[
              {
                icon: Heart,
                title: "Authentic Experiences",
                description: "We're travelers building for travelers. Every feature is tested on real journeys and shaped by genuine needs."
              },
              {
                icon: Users,
                title: "Community First",
                description: "Our early community helps shape every decision. We grow together, learn together, and explore together."
              },
              {
                icon: Zap,
                title: "Genuine Innovation",
                description: "No artificial hype or fake promises. Just sincere solutions to real travel planning challenges."
              },
              {
                icon: Globe,
                title: "Accessible Travel",
                description: "Making travel planning and experiences more accessible and enjoyable for everyone, regardless of experience level."
              }
            ].map((value, index) => (
              <Card key={index} className="p-6 sm:p-8 text-center hover:shadow-lg transition-shadow">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-orange-100 rounded-lg mb-4">
                  <value.icon className="h-6 w-6 text-orange-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">{value.title}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
              </Card>
            ))}
          </div>
        </section>

        {/* Our Mission */}
        <section className="mb-16 sm:mb-20">
          <Card className="p-8 sm:p-12 bg-gradient-to-r from-orange-50 to-pink-50 border-orange-200">
            <div className="text-center">
              <Target className="h-12 w-12 text-orange-500 mx-auto mb-6" />
              <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-xl sm:text-2xl text-gray-700 leading-relaxed max-w-4xl mx-auto mb-8">
                "To make travel planning and experiences more accessible, enjoyable, and authentic for everyone. 
                We're just starting our journey, but we're committed to growing alongside our community of travelers."
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-gray-600">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Genuine passion for exploration</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Community-driven development</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Authentic travel experiences</span>
                </div>
              </div>
            </div>
          </Card>
        </section>

        {/* Early Community */}
        <section className="mb-16 sm:mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Join Our Early Community</h2>
            <p className="text-xl text-gray-600">Help us shape the future of travel experiences</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="p-8 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Why Join Early?</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Direct influence on feature development</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Early access to new features and updates</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Connect with like-minded travelers</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Help build something meaningful together</span>
                </li>
              </ul>
            </Card>

            <Card className="p-8 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">What We Promise</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>No fake promises or artificial hype</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Transparent development process</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>Regular updates and community feedback</span>
                </li>
                <li className="flex items-start">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>A sincere platform created by travelers, for travelers</span>
                </li>
              </ul>
            </Card>
          </div>
        </section>

        {/* Contact CTA */}
        <section>
          <Card className="p-8 sm:p-12 text-center bg-white">
            <Mail className="h-12 w-12 text-orange-500 mx-auto mb-6" />
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">Ready to Join Our Journey?</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-8">
              We're just starting our journey, but we're committed to growing alongside our community of travelers. 
              Join us in building something meaningful together.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <Link href="/signup">
                <Button size="lg" className="btn-primary">
                  <Users className="h-5 w-5 mr-2" />
                  Join Our Early Community
                </Button>
              </Link>
              <a href="mailto:<EMAIL>">
                <Button size="lg" variant="outline">
                  <Mail className="h-5 w-5 mr-2" />
                  Get in Touch
                </Button>
              </a>
            </div>
            <p className="text-sm text-gray-500 mt-6">
              No spam, no fake promises - just authentic updates from fellow travelers building something special.
            </p>
          </Card>
        </section>
      </div>

      {/* Footer Navigation */}
      <div className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <Link href="/terms" className="hover:text-orange-600 transition-colors">
                Terms of Service
              </Link>
              <span>•</span>
              <Link href="/privacy" className="hover:text-orange-600 transition-colors">
                Privacy Policy
              </Link>
              <span>•</span>
              <Link href="/contact" className="hover:text-orange-600 transition-colors">
                Contact
              </Link>
            </div>
            <div className="text-sm text-gray-500">
              © 2024 TravelViz. Built with ❤️ by travelers, for travelers.
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}