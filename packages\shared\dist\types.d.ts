import { z } from 'zod';
export declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    name: z.ZodString;
    avatar: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.Zod<PERSON>ate;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    avatar?: string | undefined;
}, {
    id: string;
    email: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    avatar?: string | undefined;
}>;
export type User = z.infer<typeof UserSchema>;
export declare const TripSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    title: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
    startDate: z.ZodDate;
    endDate: z.ZodDate;
    destinations: z.ZodArray<z.ZodString, "many">;
    isPublic: z.<PERSON>ault<z.ZodBoolean>;
    createdAt: z.Zod<PERSON>ate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodType<PERSON>ny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    title: string;
    startDate: Date;
    endDate: Date;
    destinations: string[];
    isPublic: boolean;
    description?: string | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    title: string;
    startDate: Date;
    endDate: Date;
    destinations: string[];
    description?: string | undefined;
    isPublic?: boolean | undefined;
}>;
export type Trip = z.infer<typeof TripSchema>;
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface ErrorResponse {
    success: false;
    error: string;
    message?: string;
    code?: string;
}
