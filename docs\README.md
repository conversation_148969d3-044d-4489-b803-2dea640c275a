# TravelViz Documentation

Welcome to the TravelViz documentation! This directory contains comprehensive guides for developing, deploying, and maintaining the TravelViz platform.

## 📚 Documentation Structure

### Getting Started
- [Setup Guide](./setup.md) - Complete development environment setup
- [Architecture Overview](./architecture.md) - System design and component relationships
- [API Documentation](./api.md) - Hub API endpoints and usage

### Development
- [Development Guide](./development.md) - Development workflows and best practices
- [Testing Guide](./testing.md) - Testing strategies and implementation
- [Deployment Guide](./deployment.md) - Production deployment instructions

### Features
- [AI Import Engine](./features/ai-import.md) - AI conversation parsing system
- [Timeline & Maps](./features/timeline-maps.md) - Visual timeline and Mapbox integration
- [Price Tracking](./features/price-tracking.md) - Automated price monitoring
- [Sharing System](./features/sharing.md) - Viral sharing mechanics
- [Offline Support](./features/offline.md) - Mobile offline capabilities

### Integrations
- [Supabase Integration](./integrations/supabase.md) - Database and authentication
- [Flight APIs](./integrations/flights.md) - Duffel and Travelpayouts integration
- [Hotel APIs](./integrations/hotels.md) - Hotel booking providers
- [Mapbox Integration](./integrations/mapbox.md) - Maps and offline functionality
- [OpenRouter AI](./integrations/openrouter.md) - AI parsing service

### Operations
- [Monitoring](./operations/monitoring.md) - System monitoring and alerting
- [Performance](./operations/performance.md) - Performance optimization
- [Security](./operations/security.md) - Security best practices
- [Troubleshooting](./operations/troubleshooting.md) - Common issues and solutions

## 🚀 Quick Start

1. **Setup Development Environment**
   ```bash
   # Clone and install dependencies
   git clone <repository-url>
   cd travelviz
   pnpm install
   
   # Copy environment files
   cp packages/hub/.env.example packages/hub/.env
   cp packages/web/.env.example packages/web/.env
   
   # Start development servers
   pnpm dev
   ```

2. **Access Applications**
   - Web Frontend: http://localhost:3000
   - Hub API: http://localhost:3001
   - API Health Check: http://localhost:3001/health

3. **Verify Setup**
   - Check that both services start without errors
   - Verify TypeScript compilation
   - Test API connectivity between web and hub

## 📋 Project Structure

```
travelviz/
├── packages/
│   ├── hub/          # Express.js backend API
│   ├── web/          # Next.js frontend application
│   ├── mobile/       # React Native mobile app
│   └── shared/       # Shared types and utilities
├── docs/             # Documentation
├── .github/          # GitHub workflows and templates
└── package.json      # Monorepo configuration
```

## 🔧 Key Technologies

- **Backend**: Node.js, Express.js, TypeScript
- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Mobile**: React Native, Expo
- **Database**: PostgreSQL (Supabase)
- **Maps**: Mapbox GL JS
- **AI**: OpenRouter (Claude, GPT-4, Gemini)
- **Caching**: Redis (Upstash)
- **Deployment**: Render (Hub), Vercel (Web)

## 🤝 Contributing

Please read our [Development Guide](./development.md) for information on our development process, coding standards, and how to submit pull requests.

## 📞 Support

- **Issues**: Create a GitHub issue for bugs or feature requests
- **Documentation**: Check the relevant guide in this docs directory
- **Architecture Questions**: See [Architecture Overview](./architecture.md)

---

*Last updated: December 2024*
