"use client";

import { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  Search, 
  Filter, 
  Copy, 
  ArrowLeft,
  Sparkles,
  ArrowRight,
  Bot,
  MapPin,
  Calendar,
  Users,
  DollarSign,
  Clock,
  Star,
  Heart,
  Zap,
  Bell
} from 'lucide-react';
import { motion } from 'framer-motion';
import { WaitlistSignup } from '@/components/WaitlistSignup';

const promptCategories = [
  { id: 'all', label: 'All Templates', count: 30 },
  { id: 'destination', label: 'Destination Planning', count: 8 },
  { id: 'budget', label: 'Budget Optimization', count: 6 },
  { id: 'family', label: 'Family Travel', count: 5 },
  { id: 'adventure', label: 'Adventure & Outdoor', count: 4 },
  { id: 'cultural', label: 'Cultural Immersion', count: 4 },
  { id: 'business', label: 'Business Travel', count: 3 }
];

const aiPromptTemplates = [
  {
    id: 1,
    title: 'First-Time Europe Backpacker',
    description: 'Perfect for solo travelers exploring Europe on a budget',
    category: 'destination',
    difficulty: 'Beginner',
    estimatedTime: '3-4 weeks',
    budget: 'Budget ($1,000-2,000)',
    tags: ['Solo', 'Budget', 'Backpacking', 'Europe', 'Hostels'],
    prompt: `Plan a 3-4 week budget backpacking trip through Europe for a first-time solo traveler with a $1,500 total budget. Include:

• 6-8 countries with good train connections
• Budget accommodations (hostels, budget hotels)
• Free or low-cost activities and attractions
• Local food recommendations under $15/meal
• Safety tips for solo female travelers
• Packing list for backpacking
• Transportation options (Eurail pass vs individual tickets)
• Emergency contacts and backup plans
• Cultural etiquette for each country
• Hidden gems locals recommend

Focus on authentic experiences while maintaining safety and staying within budget.`,
    useCases: ['Solo female travel', 'Student gap year', 'Career break adventure'],
    expectedOutcome: 'Complete 3-4 week itinerary with daily schedules, accommodation bookings, and budget breakdown',
    popularity: 95
  },
  {
    id: 2,
    title: 'Family Disney World Vacation',
    description: 'Optimize your Disney experience for families with children',
    category: 'family',
    difficulty: 'Intermediate',
    estimatedTime: '5-7 days',
    budget: 'Mid-range ($3,000-5,000)',
    tags: ['Family', 'Theme Parks', 'Kids', 'Disney', 'Orlando'],
    prompt: `Create a comprehensive 7-day Disney World vacation plan for a family with children ages 5, 8, and 12. Include:

• Park-by-park daily schedules optimized for crowd levels
• FastPass+ strategy for must-do attractions
• Character dining reservations and experiences
• Age-appropriate activities for each child
• Rest breaks and nap schedules for younger kids
• Dining plan recommendations and snack credits
• Transportation between parks and hotels
• Packing essentials for theme park days
• Rain day backup plans and indoor activities
• Photo opportunities and memory-making moments
• Budget breakdown including tickets, food, and souvenirs

Maximize magic while minimizing stress and wait times.`,
    useCases: ['First Disney trip', 'Multi-generational family', 'Special celebrations'],
    expectedOutcome: 'Day-by-day park schedule with dining reservations and FastPass strategy',
    popularity: 88
  },
  {
    id: 3,
    title: 'Digital Nomad Southeast Asia',
    description: 'Work remotely while exploring Southeast Asia',
    category: 'business',
    difficulty: 'Advanced',
    estimatedTime: '2-3 months',
    budget: 'Mid-range ($2,000-4,000)',
    tags: ['Digital Nomad', 'Remote Work', 'Long-term', 'Asia', 'Co-working'],
    prompt: `Design a 3-month digital nomad itinerary through Southeast Asia balancing work and exploration. Include:

• 5-6 countries with strong internet infrastructure
• Co-working spaces and cafes with reliable WiFi
• Accommodations with dedicated workspace
• Visa requirements and border run strategies
• Time zone considerations for client meetings
• Cost of living breakdown for each destination
• Networking events and nomad communities
• Weekend exploration activities
• Health insurance and medical facilities
• Banking and money transfer solutions
• Productivity tools and apps for remote work
• Cultural adaptation tips for each country

Optimize for productivity while experiencing local culture.`,
    useCases: ['Remote workers', 'Freelancers', 'Location-independent entrepreneurs'],
    expectedOutcome: 'Month-by-month plan with work-life balance and visa timeline',
    popularity: 82
  },
  {
    id: 4,
    title: 'Romantic Paris Anniversary',
    description: 'Create unforgettable romantic experiences in the City of Light',
    category: 'cultural',
    difficulty: 'Intermediate',
    estimatedTime: '4-5 days',
    budget: 'Luxury ($2,500-4,000)',
    tags: ['Romance', 'Luxury', 'Anniversary', 'Paris', 'Fine Dining'],
    prompt: `Plan a romantic 4-day anniversary celebration in Paris for a couple. Include:

• Luxury hotel recommendations with romantic amenities
• Michelin-starred restaurant reservations
• Private tours of iconic landmarks
• Sunset spots and romantic viewpoints
• Seine river cruise and champagne experiences
• Art galleries and museum private tours
• Couples spa treatments and wellness
• Shopping districts for luxury goods
• Photography session locations
• Surprise elements and special touches
• Transportation in style (private car, helicopter)
• Weather contingency plans for outdoor activities

Create magical moments while avoiding tourist crowds.`,
    useCases: ['Anniversary celebrations', 'Honeymoons', 'Proposal trips'],
    expectedOutcome: 'Romantic itinerary with restaurant reservations and special experiences',
    popularity: 79
  },
  {
    id: 5,
    title: 'Adventure Hiking Patagonia',
    description: 'Epic hiking adventure through Patagonia wilderness',
    category: 'adventure',
    difficulty: 'Expert',
    estimatedTime: '2-3 weeks',
    budget: 'Mid-range ($2,500-4,000)',
    tags: ['Hiking', 'Adventure', 'Patagonia', 'Wilderness', 'Photography'],
    prompt: `Create a 2-3 week hiking adventure through Patagonia (Chile & Argentina). Include:

• Multi-day trek routes (Torres del Paine, Fitz Roy, etc.)
• Gear checklist for extreme weather conditions
• Permit requirements and booking timelines
• Weather patterns and optimal hiking windows
• Accommodation options (camping, refugios, hotels)
• Transportation between trailheads
• Photography equipment and techniques
• Safety protocols and emergency procedures
• Physical preparation and training plan
• Local guide recommendations
• Resupply points for long treks
• Cultural experiences with local communities

Balance challenging adventures with safety and logistics.`,
    useCases: ['Experienced hikers', 'Photography enthusiasts', 'Adventure couples'],
    expectedOutcome: 'Detailed trekking plan with gear lists and safety protocols',
    popularity: 76
  },
  {
    id: 6,
    title: 'Culinary Tour Vietnam',
    description: 'Immersive food journey through Vietnamese cuisine',
    category: 'cultural',
    difficulty: 'Intermediate',
    estimatedTime: '10-14 days',
    budget: 'Mid-range ($1,500-2,500)',
    tags: ['Food', 'Cooking Classes', 'Markets', 'Culture', 'Authentic'],
    prompt: `Design a 12-day culinary exploration of Vietnam from north to south. Include:

• Regional cuisine differences and specialties
• Cooking classes with local chefs
• Street food tours and market visits
• Farm-to-table experiences
• Traditional restaurant recommendations
• Food safety tips and precautions
• Dietary restriction accommodations
• Spice level guidance for sensitive palates
• Cultural dining etiquette
• Ingredient shopping and shipping home
• Recipe collection and cooking techniques
• Food photography opportunities

Experience authentic Vietnamese culture through its cuisine.`,
    useCases: ['Food enthusiasts', 'Professional chefs', 'Cultural explorers'],
    expectedOutcome: 'Culinary itinerary with cooking classes and authentic food experiences',
    popularity: 73
  },
  {
    id: 7,
    title: 'Multi-Generational Italy',
    description: 'Perfect Italy trip for grandparents, parents, and kids',
    category: 'family',
    difficulty: 'Intermediate',
    estimatedTime: '10-12 days',
    budget: 'Mid-range ($4,000-7,000)',
    tags: ['Multi-Generation', 'Accessibility', 'History', 'Culture', 'Family'],
    prompt: `Plan a 10-day Italy trip for three generations (ages 8-78) traveling together. Include:

• Accessible transportation and accommodations
• Activities suitable for different mobility levels
• Educational experiences for children
• Historical sites with engaging storytelling
• Flexible scheduling with rest periods
• Dining options for various dietary needs
• Split activities for different age groups
• Wheelchair accessibility information
• Medical facilities and pharmacy locations
• Entertainment for children during long days
• Photography opportunities for family memories
• Cultural experiences everyone can enjoy

Balance education, entertainment, and comfort for all ages.`,
    useCases: ['Family reunions', 'Grandparent trips', 'Special celebrations'],
    expectedOutcome: 'Inclusive itinerary accommodating all ages and mobility levels',
    popularity: 71
  },
  {
    id: 8,
    title: 'Solo Female Japan Adventure',
    description: 'Safe and enriching solo travel experience in Japan',
    category: 'destination',
    difficulty: 'Intermediate',
    estimatedTime: '2-3 weeks',
    budget: 'Mid-range ($2,000-3,500)',
    tags: ['Solo Female', 'Japan', 'Culture', 'Safety', 'Traditional'],
    prompt: `Create a 2-3 week solo female travel itinerary for Japan. Include:

• Safety tips and cultural guidelines
• Women-only accommodations and facilities
• Traditional experiences (tea ceremony, kimono, onsen)
• Modern attractions and pop culture sites
• Language barrier solutions and translation apps
• Public transportation navigation
• Solo dining etiquette and recommendations
• Emergency contacts and support systems
• Cultural sensitivity and respect guidelines
• Photography permissions and restrictions
• Shopping districts and unique souvenirs
• Seasonal considerations and festivals

Ensure safety while maximizing cultural immersion.`,
    useCases: ['Solo female travelers', 'Cultural enthusiasts', 'First-time Japan visitors'],
    expectedOutcome: 'Safe and culturally rich solo travel plan with safety protocols',
    popularity: 85
  },
  {
    id: 9,
    title: 'Budget Backpacking Central America',
    description: 'Affordable adventure through Central America',
    category: 'budget',
    difficulty: 'Advanced',
    estimatedTime: '4-6 weeks',
    budget: 'Budget ($800-1,500)',
    tags: ['Budget', 'Backpacking', 'Central America', 'Adventure', 'Hostels'],
    prompt: `Plan a 5-week budget backpacking trip through Central America. Include:

• Country-by-country budget breakdown
• Overland transportation routes and costs
• Budget accommodations (hostels, guesthouses)
• Free and low-cost activities
• Street food safety and recommendations
• Border crossing procedures and requirements
• Safety considerations for each country
• Packing list for tropical climate
• Health precautions and vaccinations
• Emergency fund and backup plans
• Language basics for Spanish communication
• Cultural sensitivity and local customs

Maximize experiences while minimizing costs.`,
    useCases: ['Gap year travelers', 'Budget adventurers', 'Long-term backpackers'],
    expectedOutcome: 'Detailed budget plan with daily costs and safety guidelines',
    popularity: 68
  },
  {
    id: 10,
    title: 'Luxury African Safari',
    description: 'Premium wildlife experience across East Africa',
    category: 'adventure',
    difficulty: 'Intermediate',
    estimatedTime: '10-14 days',
    budget: 'Luxury ($8,000-15,000)',
    tags: ['Luxury', 'Safari', 'Wildlife', 'Photography', 'Africa'],
    prompt: `Design a luxury 12-day African safari across Kenya and Tanzania. Include:

• Premium safari lodges and tented camps
• Private game drives and expert guides
• Big Five wildlife viewing opportunities
• Photography workshops and equipment tips
• Cultural visits to Maasai communities
• Hot air balloon safaris and scenic flights
• Luxury transportation between parks
• Fine dining experiences in the wilderness
• Spa treatments and wellness activities
• Conservation education and park fees
• Seasonal migration patterns and timing
• Health requirements and travel insurance

Create unforgettable wildlife encounters with luxury comfort.`,
    useCases: ['Luxury travelers', 'Wildlife photographers', 'Honeymoon safaris'],
    expectedOutcome: 'Premium safari itinerary with luxury accommodations and exclusive experiences',
    popularity: 74
  },
  {
    id: 11,
    title: 'Business Trip Extension Tokyo',
    description: 'Maximize personal time during business travel',
    category: 'business',
    difficulty: 'Beginner',
    estimatedTime: '3-4 days',
    budget: 'Mid-range ($800-1,200)',
    tags: ['Business Travel', 'Tokyo', 'Efficient', 'Cultural', 'Short-term'],
    prompt: `Optimize a 3-day personal extension to a Tokyo business trip. Include:

• Jet lag management and recovery strategies
• Efficient sightseeing near business districts
• Quick cultural experiences between meetings
• Business-appropriate dining recommendations
• Transportation from business hotels to attractions
• Time-efficient itinerary planning
• Professional networking opportunities
• Shopping for business gifts and souvenirs
• Relaxation and stress relief activities
• Cultural etiquette for business interactions
• Emergency business support services
• Health and wellness facilities

Balance professional obligations with personal exploration.`,
    useCases: ['Business travelers', 'Conference attendees', 'Corporate trips'],
    expectedOutcome: 'Efficient personal itinerary that complements business schedule',
    popularity: 66
  },
  {
    id: 12,
    title: 'Wellness Retreat Bali',
    description: 'Rejuvenating mind-body wellness experience',
    category: 'cultural',
    difficulty: 'Beginner',
    estimatedTime: '7-10 days',
    budget: 'Mid-range ($1,200-2,500)',
    tags: ['Wellness', 'Yoga', 'Meditation', 'Spa', 'Healthy'],
    prompt: `Create a 7-day wellness and rejuvenation retreat in Bali. Include:

• Yoga and meditation retreat centers
• Spa treatments and traditional healing
• Healthy cuisine and detox programs
• Mindfulness activities and workshops
• Nature excursions and outdoor meditation
• Cultural ceremonies and spiritual experiences
• Accommodation with wellness amenities
• Digital detox recommendations
• Fitness activities and beach workouts
• Holistic health consultations
• Sustainable and eco-friendly options
• Personal growth and reflection time

Focus on mental, physical, and spiritual well-being.`,
    useCases: ['Wellness seekers', 'Stress relief', 'Personal transformation'],
    expectedOutcome: 'Comprehensive wellness plan with daily activities and treatments',
    popularity: 77
  },
  {
    id: 13,
    title: 'Road Trip American Southwest',
    description: 'Epic road trip through iconic American landscapes',
    category: 'adventure',
    difficulty: 'Intermediate',
    estimatedTime: '2-3 weeks',
    budget: 'Mid-range ($2,000-3,500)',
    tags: ['Road Trip', 'National Parks', 'Photography', 'Camping', 'USA'],
    prompt: `Plan a 2-3 week road trip through the American Southwest. Include:

• Route planning through national parks
• Vehicle rental and insurance considerations
• Camping vs hotel accommodation options
• Photography locations and golden hour timing
• Hiking trails and difficulty levels
• Weather considerations by season
• Fuel stops and vehicle maintenance
• Emergency preparedness and safety
• Park passes and reservation requirements
• Local dining and supply stops
• Cultural sites and Native American heritage
• Stargazing opportunities and dark sky areas

Capture the beauty of America's most stunning landscapes.`,
    useCases: ['Photography enthusiasts', 'Nature lovers', 'Adventure couples'],
    expectedOutcome: 'Detailed road trip route with camping and photography locations',
    popularity: 81
  },
  {
    id: 14,
    title: 'Group Friends Ibiza',
    description: 'Ultimate party and relaxation trip for friend groups',
    category: 'destination',
    difficulty: 'Intermediate',
    estimatedTime: '5-7 days',
    budget: 'Mid-range ($1,500-3,000)',
    tags: ['Group Travel', 'Nightlife', 'Beach', 'Party', 'Friends'],
    prompt: `Organize a 6-day Ibiza trip for a group of 8 friends. Include:

• Group accommodation options (villas, apartments)
• Beach clubs and day party venues
• Nightlife and club recommendations
• Group dining reservations and experiences
• Boat trips and water activities
• Relaxation and recovery time
• Transportation for large groups
• Budget splitting and expense management
• Group activity coordination
• Safety protocols for nightlife
• Alternative activities for non-partiers
• Photography and memory-making opportunities

Balance party time with relaxation and group bonding.`,
    useCases: ['Bachelor/bachelorette parties', 'Friend reunions', 'Celebration trips'],
    expectedOutcome: 'Group itinerary with nightlife, activities, and accommodation coordination',
    popularity: 70
  },
  {
    id: 15,
    title: 'Cultural Immersion Morocco',
    description: 'Deep dive into Moroccan culture and traditions',
    category: 'cultural',
    difficulty: 'Advanced',
    estimatedTime: '10-14 days',
    budget: 'Mid-range ($1,800-3,000)',
    tags: ['Culture', 'Morocco', 'Traditional', 'Markets', 'Desert'],
    prompt: `Design a 12-day cultural immersion experience in Morocco. Include:

• Traditional riad accommodations
• Medina navigation and market tours
• Cooking classes with local families
• Desert camping and camel trekking
• Artisan workshops (pottery, weaving, metalwork)
• Language basics and cultural etiquette
• Religious site visits and respect protocols
• Traditional music and dance experiences
• Hammam and traditional spa treatments
• Photography permissions and cultural sensitivity
• Bargaining techniques and fair pricing
• Safety considerations for solo travelers

Experience authentic Moroccan culture with respect and understanding.`,
    useCases: ['Cultural enthusiasts', 'Photography travelers', 'Spiritual seekers'],
    expectedOutcome: 'Immersive cultural itinerary with authentic local experiences',
    popularity: 75
  },
  {
    id: 16,
    title: 'Winter Northern Lights Iceland',
    description: 'Chase the aurora borealis in Iceland\'s winter wonderland',
    category: 'adventure',
    difficulty: 'Intermediate',
    estimatedTime: '7-10 days',
    budget: 'Mid-range ($2,500-4,000)',
    tags: ['Northern Lights', 'Winter', 'Photography', 'Iceland', 'Nature'],
    prompt: `Plan a 8-day winter Northern Lights adventure in Iceland. Include:

• Aurora forecast and optimal viewing locations
• Winter driving safety and vehicle requirements
• Cold weather clothing and gear recommendations
• Photography equipment for aurora and landscapes
• Geothermal hot springs and winter activities
• Ice caves and glacier exploration
• Winter daylight optimization (short days)
• Emergency preparedness for harsh weather
• Accommodation with aurora wake-up calls
• Cultural experiences and local cuisine
• Alternative indoor activities for cloudy nights
• Professional photography tours and workshops

Maximize chances of seeing the Northern Lights while staying safe.`,
    useCases: ['Aurora hunters', 'Winter photographers', 'Adventure couples'],
    expectedOutcome: 'Winter itinerary optimized for Northern Lights viewing and safety',
    popularity: 83
  },
  {
    id: 17,
    title: 'Sustainable Travel Costa Rica',
    description: 'Eco-friendly adventure through Costa Rica\'s biodiversity',
    category: 'adventure',
    difficulty: 'Intermediate',
    estimatedTime: '10-14 days',
    budget: 'Mid-range ($2,000-3,500)',
    tags: ['Eco-Tourism', 'Sustainability', 'Wildlife', 'Conservation', 'Adventure'],
    prompt: `Create a 12-day sustainable travel experience in Costa Rica. Include:

• Eco-lodges and sustainable accommodations
• Wildlife conservation project visits
• Responsible wildlife viewing guidelines
• Carbon offset transportation options
• Local community tourism initiatives
• Organic farm visits and farm-to-table dining
• Reforestation and conservation activities
• Sustainable adventure activities (zip-lining, hiking)
• Plastic-free travel tips and alternatives
• Supporting local artisans and businesses
• Educational experiences about biodiversity
• Leave No Trace principles and practices

Travel responsibly while supporting conservation efforts.`,
    useCases: ['Eco-conscious travelers', 'Conservation supporters', 'Educational trips'],
    expectedOutcome: 'Sustainable itinerary supporting conservation and local communities',
    popularity: 72
  },
  {
    id: 18,
    title: 'Luxury Train Journey Europe',
    description: 'Elegant rail travel across European capitals',
    category: 'destination',
    difficulty: 'Beginner',
    estimatedTime: '10-14 days',
    budget: 'Luxury ($8,000-12,000)',
    tags: ['Luxury', 'Train Travel', 'Europe', 'Elegant', 'Scenic'],
    prompt: `Design a 12-day luxury train journey across Europe. Include:

• Premium train routes and cabin selections
• City stopover planning and luxury hotels
• Fine dining experiences on trains and in cities
• Cultural excursions at each destination
• Luggage handling and travel logistics
• Dress codes and etiquette for luxury travel
• Photography opportunities from scenic routes
• Spa and wellness services on trains
• Private guided tours in major cities
• Shopping districts and luxury boutiques
• Theater and cultural event bookings
• Travel insurance for luxury experiences

Experience Europe's grandeur through elegant rail travel.`,
    useCases: ['Luxury travelers', 'Anniversary trips', 'Retirement celebrations'],
    expectedOutcome: 'Luxury rail itinerary with premium accommodations and experiences',
    popularity: 69
  },
  {
    id: 19,
    title: 'Festival Circuit Europe',
    description: 'Music and cultural festival hopping across Europe',
    category: 'cultural',
    difficulty: 'Advanced',
    estimatedTime: '6-8 weeks',
    budget: 'Mid-range ($3,000-5,000)',
    tags: ['Festivals', 'Music', 'Culture', 'Youth', 'Summer'],
    prompt: `Plan a 7-week European festival circuit during summer. Include:

• Major music festival calendar and locations
• Ticket purchasing strategies and early bird deals
• Transportation between festival cities
• Accommodation options (camping, hostels, hotels)
• Festival survival gear and packing lists
• Budget management for extended travel
• Cultural festivals and local celebrations
• Recovery time between intense festivals
• Safety protocols for large crowds
• Meeting fellow travelers and festival communities
• Photography and social media opportunities
• Alternative activities for non-festival days

Experience Europe's vibrant festival culture and music scene.`,
    useCases: ['Music lovers', 'Young travelers', 'Cultural enthusiasts'],
    expectedOutcome: 'Festival calendar with logistics and accommodation planning',
    popularity: 67
  },
  {
    id: 20,
    title: 'Retirement Dream Cruise',
    description: 'Relaxing cruise experience for active retirees',
    category: 'destination',
    difficulty: 'Beginner',
    estimatedTime: '14-21 days',
    budget: 'Mid-range ($4,000-8,000)',
    tags: ['Cruise', 'Retirement', 'Relaxation', 'Luxury', 'Accessible'],
    prompt: `Design a 2-3 week cruise experience for active retirees. Include:

• Cruise line selection based on demographics and interests
• Cabin categories and accessibility features
• Shore excursion planning for various mobility levels
• Onboard activities and entertainment options
• Dining options and dietary accommodations
• Health and medical facilities onboard
• Packing essentials for extended cruise travel
• Pre and post-cruise city extensions
• Travel insurance and health considerations
• Social activities and meeting fellow travelers
• Photography workshops and scenic viewing
• Cultural enrichment and educational programs

Enjoy relaxing luxury travel with engaging activities.`,
    useCases: ['Retirees', 'Anniversary celebrations', 'Accessible travel'],
    expectedOutcome: 'Cruise itinerary with shore excursions and onboard activities',
    popularity: 64
  },
  {
    id: 21,
    title: 'Extreme Sports New Zealand',
    description: 'Adrenaline-packed adventure sports across New Zealand',
    category: 'adventure',
    difficulty: 'Expert',
    estimatedTime: '2-3 weeks',
    budget: 'Mid-range ($3,500-5,500)',
    tags: ['Extreme Sports', 'Adrenaline', 'New Zealand', 'Adventure', 'Outdoors'],
    prompt: `Create a 3-week extreme sports adventure in New Zealand. Include:

• Bungee jumping, skydiving, and white-water rafting
• Safety certifications and experience requirements
• Equipment rental and professional instruction
• Insurance coverage for extreme activities
• Physical fitness preparation and training
• Weather considerations and seasonal timing
• Recovery time between intense activities
• Scenic locations for adventure sports
• Photography and video documentation
• Emergency medical facilities and protocols
• Transportation between adventure locations
• Alternative activities for rest days

Push your limits safely in the adventure capital of the world.`,
    useCases: ['Adrenaline junkies', 'Adventure athletes', 'Thrill seekers'],
    expectedOutcome: 'Extreme sports itinerary with safety protocols and training requirements',
    popularity: 78
  },
  {
    id: 22,
    title: 'Art and Architecture Italy',
    description: 'Renaissance art and architectural masterpieces tour',
    category: 'cultural',
    difficulty: 'Intermediate',
    estimatedTime: '12-14 days',
    budget: 'Mid-range ($2,500-4,000)',
    tags: ['Art', 'Architecture', 'Renaissance', 'Museums', 'History'],
    prompt: `Design a 12-day art and architecture focused tour of Italy. Include:

• Major museums and gallery reservations
• Private art tours and expert guides
• Architectural walking tours in historic cities
• Artist studio visits and workshops
• Art history context and educational materials
• Photography permissions and restrictions
• Sketch books and art supply recommendations
• Cultural etiquette in religious and historic sites
• Transportation between art destinations
• Accommodation near major cultural sites
• Dining in historically significant locations
• Art-focused shopping and authentic reproductions

Immerse yourself in Italy's unparalleled artistic heritage.`,
    useCases: ['Art enthusiasts', 'Architecture students', 'Cultural travelers'],
    expectedOutcome: 'Art-focused itinerary with museum reservations and expert guides',
    popularity: 71
  },
  {
    id: 23,
    title: 'Spiritual Journey India',
    description: 'Transformative spiritual experience across sacred India',
    category: 'cultural',
    difficulty: 'Advanced',
    estimatedTime: '3-4 weeks',
    budget: 'Budget ($1,000-2,000)',
    tags: ['Spiritual', 'Meditation', 'Yoga', 'Temples', 'Transformation'],
    prompt: `Plan a 3-week spiritual journey through India. Include:

• Sacred sites and temple visits with proper protocols
• Meditation retreats and ashram experiences
• Yoga teacher training and practice sessions
• Spiritual guides and authentic teachers
• Cultural sensitivity and religious respect
• Health precautions and medical preparations
• Vegetarian and sattvic food recommendations
• Accommodation in spiritual communities
• Transportation to remote spiritual locations
• Donation guidelines and supporting communities
• Personal reflection and journaling practices
• Integration practices for returning home

Embark on a transformative journey of self-discovery.`,
    useCases: ['Spiritual seekers', 'Yoga practitioners', 'Personal transformation'],
    expectedOutcome: 'Spiritual itinerary with meditation retreats and sacred site visits',
    popularity: 73
  },
  {
    id: 24,
    title: 'Photography Expedition Antarctica',
    description: 'Once-in-a-lifetime photography expedition to Antarctica',
    category: 'adventure',
    difficulty: 'Expert',
    estimatedTime: '14-18 days',
    budget: 'Luxury ($12,000-20,000)',
    tags: ['Photography', 'Antarctica', 'Wildlife', 'Expedition', 'Unique'],
    prompt: `Organize a 16-day photography expedition to Antarctica. Include:

• Expedition cruise selection and cabin categories
• Photography equipment for extreme conditions
• Wildlife photography techniques and ethics
• Cold weather gear and protection systems
• Professional photography workshops onboard
• Zodiac landing procedures and safety protocols
• Environmental impact and conservation awareness
• Travel insurance for expedition travel
• Pre-expedition preparation and fitness requirements
• Post-processing workshops and editing techniques
• Cultural preparation for expedition community
• Emergency procedures and medical facilities

Capture the pristine beauty of Earth's last wilderness.`,
    useCases: ['Professional photographers', 'Wildlife enthusiasts', 'Bucket list travelers'],
    expectedOutcome: 'Expedition photography plan with equipment lists and workshop schedule',
    popularity: 89
  },
  {
    id: 25,
    title: 'Foodie Tour Southeast Asia',
    description: 'Culinary adventure through diverse Southeast Asian cuisines',
    category: 'cultural',
    difficulty: 'Intermediate',
    estimatedTime: '3-4 weeks',
    budget: 'Mid-range ($2,000-3,500)',
    tags: ['Food', 'Street Food', 'Cooking', 'Markets', 'Authentic'],
    prompt: `Create a 3-week culinary tour through Southeast Asia. Include:

• Country-by-country cuisine specialties and differences
• Street food safety guidelines and recommendations
• Cooking classes with local chefs and families
• Market tours and ingredient identification
• Food photography techniques and etiquette
• Dietary restriction accommodations and alternatives
• Spice tolerance building and heat management
• Cultural dining customs and table manners
• Food-focused accommodation near culinary districts
• Transportation between food destinations
• Recipe collection and cooking technique notes
• Food souvenir shopping and shipping guidelines

Taste your way through the world's most diverse food region.`,
    useCases: ['Food enthusiasts', 'Culinary professionals', 'Cultural explorers'],
    expectedOutcome: 'Culinary itinerary with cooking classes and authentic food experiences',
    popularity: 80
  },
  {
    id: 26,
    title: 'Volunteer Travel Africa',
    description: 'Meaningful volunteer work combined with cultural immersion',
    category: 'cultural',
    difficulty: 'Advanced',
    estimatedTime: '2-4 weeks',
    budget: 'Mid-range ($2,500-4,500)',
    tags: ['Volunteer', 'Community Service', 'Africa', 'Meaningful', 'Impact'],
    prompt: `Design a 3-week volunteer travel experience in Africa. Include:

• Reputable volunteer organizations and project selection
• Skills assessment and meaningful contribution opportunities
• Cultural preparation and sensitivity training
• Health requirements and vaccination schedules
• Accommodation with volunteer communities
• Local language basics and communication
• Sustainable tourism and community impact
• Personal safety and security protocols
• Cultural exchange and learning opportunities
• Documentation and reflection practices
• Post-volunteer integration and continued support
• Ethical considerations and avoiding voluntourism

Make a positive impact while gaining life-changing experiences.`,
    useCases: ['Gap year students', 'Career changers', 'Social impact travelers'],
    expectedOutcome: 'Volunteer program with meaningful projects and cultural immersion',
    popularity: 65
  },
  {
    id: 27,
    title: 'Luxury Wellness Maldives',
    description: 'Ultimate luxury wellness retreat in tropical paradise',
    category: 'destination',
    difficulty: 'Beginner',
    estimatedTime: '7-10 days',
    budget: 'Luxury ($8,000-15,000)',
    tags: ['Luxury', 'Wellness', 'Spa', 'Tropical', 'Relaxation'],
    prompt: `Plan a 7-day luxury wellness retreat in the Maldives. Include:

• Overwater villa selection with spa amenities
• Personalized wellness programs and assessments
• World-class spa treatments and therapies
• Healthy gourmet cuisine and detox programs
• Water sports and fitness activities
• Meditation and mindfulness sessions
• Couples treatments and romantic experiences
• Photography sessions in paradise settings
• Sustainable luxury and eco-conscious choices
• Transportation via seaplane or speedboat
• Weather considerations and seasonal timing
• Digital detox and disconnection strategies

Experience ultimate luxury while rejuvenating mind and body.`,
    useCases: ['Luxury wellness seekers', 'Honeymoons', 'Stress relief'],
    expectedOutcome: 'Luxury wellness itinerary with spa treatments and relaxation activities',
    popularity: 86
  },
  {
    id: 28,
    title: 'Historical Battlefield Tour Europe',
    description: 'Educational journey through European historical battlefields',
    category: 'cultural',
    difficulty: 'Intermediate',
    estimatedTime: '10-14 days',
    budget: 'Mid-range ($2,200-3,800)',
    tags: ['History', 'Education', 'Battlefields', 'Museums', 'Memorial'],
    prompt: `Create a 12-day historical battlefield tour across Europe. Include:

• Major battlefield sites and historical significance
• Expert historian guides and educational tours
• Museum visits and artifact collections
• Memorial services and remembrance protocols
• Historical context and timeline preparation
• Accommodation near historical sites
• Transportation between battlefield locations
• Photography permissions and respectful documentation
• Educational materials and recommended reading
• Cultural sensitivity at memorial sites
• Local historical societies and expert contacts
• Weather considerations for outdoor sites

Honor history while gaining deep educational insights.`,
    useCases: ['History enthusiasts', 'Educational travelers', 'Veterans'],
    expectedOutcome: 'Educational historical tour with expert guides and memorial visits',
    popularity: 62
  },
  {
    id: 29,
    title: 'Island Hopping Greece',
    description: 'Romantic island hopping through the Greek Cyclades',
    category: 'destination',
    difficulty: 'Intermediate',
    estimatedTime: '10-14 days',
    budget: 'Mid-range ($2,500-4,000)',
    tags: ['Islands', 'Greece', 'Romance', 'Beaches', 'Culture'],
    prompt: `Plan a 12-day Greek island hopping adventure through the Cyclades. Include:

• Island selection based on interests and ferry connections
• Ferry schedules and inter-island transportation
• Accommodation variety (hotels, villas, traditional houses)
• Beach recommendations and water activities
• Historical sites and archaeological tours
• Local cuisine and taverna recommendations
• Sunset viewing locations and romantic spots
• Photography opportunities and scenic viewpoints
• Cultural festivals and local celebrations
• Shopping for authentic Greek products
• Weather considerations and seasonal timing
• Budget management across different islands

Experience the magic of Greek island life and culture.`,
    useCases: ['Romantic getaways', 'Cultural travelers', 'Beach lovers'],
    expectedOutcome: 'Island hopping itinerary with ferry schedules and accommodation variety',
    popularity: 84
  },
  {
    id: 30,
    title: 'Winter Sports Alps Adventure',
    description: 'Epic skiing and winter sports across the European Alps',
    category: 'adventure',
    difficulty: 'Advanced',
    estimatedTime: '10-14 days',
    budget: 'Mid-range ($3,000-5,000)',
    tags: ['Skiing', 'Winter Sports', 'Alps', 'Adventure', 'Mountains'],
    prompt: `Design a 12-day winter sports adventure across the European Alps. Include:

• Multi-resort ski passes and lift ticket strategies
• Accommodation options (ski-in/ski-out, chalets, hotels)
• Equipment rental vs purchase recommendations
• Ski lessons and technique improvement programs
• Off-piste and backcountry skiing safety protocols
• Après-ski culture and mountain dining experiences
• Alternative winter activities (snowshoeing, ice climbing)
• Weather monitoring and avalanche safety awareness
• Transportation between ski resorts
• Photography in alpine environments
• Fitness preparation and injury prevention
• Budget management for expensive ski destinations

Experience world-class skiing in the most beautiful mountains.`,
    useCases: ['Ski enthusiasts', 'Winter sports athletes', 'Mountain lovers'],
    expectedOutcome: 'Ski itinerary with resort recommendations and safety protocols',
    popularity: 77
  }
];

export default function TemplatesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [copiedPrompt, setCopiedPrompt] = useState<number | null>(null);

  const filteredTemplates = aiPromptTemplates.filter(template => {
    const matchesSearch = template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const copyPrompt = (templateId: number, prompt: string) => {
    // Show coming soon message instead of copying
    setCopiedPrompt(templateId);
    setTimeout(() => setCopiedPrompt(null), 3000);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-orange-100 text-orange-800';
      case 'Expert': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-2">
              <div className="p-2 bg-orange-500 rounded-lg">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">TravelViz</span>
            </Link>
            <div className="flex items-center">
              <WaitlistSignup size="sm" />
            </div>
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              30 AI Travel Planning Templates
            </h1>
            <p className="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
              Copy these proven AI prompts to create amazing travel itineraries. Each template is crafted 
              by travel experts and optimized for the best AI responses.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search by destination, travel style, or activity..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 pr-4 py-4 text-lg bg-white text-gray-900 border-0 rounded-xl shadow-lg"
                />
              </div>
            </div>

            {/* Quick Stats */}
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 mt-8 text-white/90">
              <div className="flex items-center space-x-2">
                <Bot className="h-5 w-5" />
                <span>30 Expert-Crafted Prompts</span>
              </div>
              <div className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>Instant Copy & Use</span>
              </div>
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5" />
                <span>Proven Results</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Coming Soon Banner */}
      <div className="bg-orange-50 border-y border-orange-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="bg-orange-100 rounded-full p-2">
                <Bell className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <h3 className="font-semibold text-orange-900">Templates Preview - Coming Soon!</h3>
                <p className="text-sm text-orange-700">These templates are in development. Join our waitlist to get early access.</p>
              </div>
            </div>
            <div className="flex-shrink-0">
              <WaitlistSignup size="sm" className="min-w-[300px]" />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Categories */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {promptCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-orange-50 text-orange-600 border border-orange-200'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <span>{category.label}</span>
                    <Badge variant="secondary" className="text-xs">
                      {category.count}
                    </Badge>
                  </button>
                ))}
              </div>
            </Card>

            {/* How to Use */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">How to Use</h3>
              <div className="space-y-3 text-sm text-gray-600">
                <div className="flex items-start space-x-2">
                  <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold mt-0.5">1</div>
                  <span>Choose a template that matches your trip style</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold mt-0.5">2</div>
                  <span>Copy the AI prompt to your clipboard</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold mt-0.5">3</div>
                  <span>Paste into ChatGPT, Claude, or your favorite AI</span>
                </div>
                <div className="flex items-start space-x-2">
                  <div className="w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center text-white text-xs font-bold mt-0.5">4</div>
                  <span>Customize the details for your specific trip</span>
                </div>
              </div>
            </Card>

            {/* Popular Templates */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Most Popular</h3>
              <div className="space-y-3">
                {aiPromptTemplates
                  .sort((a, b) => b.popularity - a.popularity)
                  .slice(0, 5)
                  .map((template) => (
                    <div key={template.id} className="text-sm">
                      <div className="font-medium text-gray-900 truncate">{template.title}</div>
                      <div className="flex items-center space-x-2 text-gray-500">
                        <Heart className="h-3 w-3" />
                        <span>{template.popularity}% success rate</span>
                      </div>
                    </div>
                  ))}
              </div>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3 space-y-6">
            {/* Results Header */}
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900">
                  {filteredTemplates.length} AI Prompt Templates
                </h2>
                <p className="text-gray-600">Expert-crafted prompts for perfect travel planning</p>
              </div>
            </div>

            {/* Templates Grid */}
            <div className="space-y-6">
              {filteredTemplates.map((template, index) => (
                <motion.div
                  key={template.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05, duration: 0.4 }}
                >
                  <Card className="p-6 hover:shadow-lg transition-shadow">
                    <div className="space-y-4">
                      {/* Header */}
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-xl font-bold text-gray-900">{template.title}</h3>
                            <Badge className={getDifficultyColor(template.difficulty)}>
                              {template.difficulty}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {template.popularity}% success
                            </Badge>
                          </div>
                          <p className="text-gray-600 mb-3">{template.description}</p>
                          
                          <div className="flex flex-wrap gap-2 mb-4">
                            {template.tags.map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Trip Details */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 py-4 bg-gray-50 rounded-lg">
                        <div className="text-center">
                          <Clock className="h-4 w-4 text-gray-500 mx-auto mb-1" />
                          <div className="text-sm font-medium text-gray-900">{template.estimatedTime}</div>
                          <div className="text-xs text-gray-500">Duration</div>
                        </div>
                        <div className="text-center">
                          <DollarSign className="h-4 w-4 text-gray-500 mx-auto mb-1" />
                          <div className="text-sm font-medium text-gray-900">{template.budget}</div>
                          <div className="text-xs text-gray-500">Budget</div>
                        </div>
                        <div className="text-center">
                          <Users className="h-4 w-4 text-gray-500 mx-auto mb-1" />
                          <div className="text-sm font-medium text-gray-900">{template.useCases.length}</div>
                          <div className="text-xs text-gray-500">Use Cases</div>
                        </div>
                        <div className="text-center">
                          <Star className="h-4 w-4 text-gray-500 mx-auto mb-1" />
                          <div className="text-sm font-medium text-gray-900">{template.popularity}%</div>
                          <div className="text-xs text-gray-500">Success Rate</div>
                        </div>
                      </div>

                      {/* Use Cases */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">Perfect for:</h4>
                        <div className="flex flex-wrap gap-2">
                          {template.useCases.map((useCase, idx) => (
                            <span key={idx} className="text-sm bg-blue-50 text-blue-700 px-2 py-1 rounded">
                              {useCase}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Expected Outcome */}
                      <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                        <h4 className="font-semibold text-green-900 mb-2">Expected Outcome</h4>
                        <p className="text-green-800 text-sm">{template.expectedOutcome}</p>
                      </div>

                      {/* AI Prompt */}
                      <div className="bg-gray-900 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-white flex items-center">
                            <Bot className="h-4 w-4 text-orange-400 mr-2" />
                            AI Prompt Template
                          </h4>
                          <Button
                            size="sm"
                            onClick={() => copyPrompt(template.id, template.prompt)}
                            className={`${
                              copiedPrompt === template.id 
                                ? 'bg-yellow-500 hover:bg-yellow-600' 
                                : 'bg-orange-500 hover:bg-orange-600'
                            } text-white`}
                          >
                            {copiedPrompt === template.id ? (
                              <>
                                <Bell className="h-3 w-3 mr-1" />
                                Coming Soon!
                              </>
                            ) : (
                              <>
                                <Copy className="h-3 w-3 mr-1" />
                                Copy Prompt
                              </>
                            )}
                          </Button>
                        </div>
                        <div className="bg-gray-800 rounded p-3 max-h-48 overflow-y-auto">
                          <pre className="text-green-300 text-sm whitespace-pre-wrap font-mono leading-relaxed">
                            {template.prompt}
                          </pre>
                        </div>
                        <div className="mt-3 text-xs text-gray-400">
                          💡 Tip: Customize the details (dates, budget, preferences) to match your specific trip
                        </div>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white py-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Plan Your Perfect Trip?</h2>
          <p className="text-xl text-white/90 mb-8">
            Join our waitlist to get early access to these AI prompts and transform them into beautiful, shareable travel plans
          </p>
          <div className="max-w-lg mx-auto">
            <WaitlistSignup 
              size="lg" 
              variant="secondary"
              showBenefits={false}
            />
          </div>
          <p className="text-sm text-white/80 mt-6">
            Get notified when TravelViz launches with full template functionality
          </p>
        </div>
      </div>
    </div>
  );
}