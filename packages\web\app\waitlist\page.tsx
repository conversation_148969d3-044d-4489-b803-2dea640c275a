"use client";

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowLeft, <PERSON>rk<PERSON>, CheckCircle, Clock, Users, Star } from 'lucide-react';
import { WaitlistSignup } from '@/components/WaitlistSignup';

export default function WaitlistPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-pink-50">
      {/* Navigation */}
      <div className="relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-2 group">
              <ArrowLeft className="h-4 w-4 text-gray-600 group-hover:text-orange-600 transition-colors" />
              <span className="text-gray-600 group-hover:text-orange-600 transition-colors">Back to Home</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center space-y-8"
        >
          {/* Icon */}
          <div className="flex justify-center">
            <div className="bg-gradient-to-r from-orange-500 to-pink-500 rounded-full p-6">
              <Sparkles className="h-12 w-12 text-white" />
            </div>
          </div>

          {/* Headline */}
          <div className="space-y-4">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold text-gray-900 leading-tight">
              Join the
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 to-pink-500">
                TravelViz Waitlist
              </span>
            </h1>
            <p className="text-xl sm:text-2xl text-gray-600 max-w-3xl mx-auto">
              Be among the first to transform your AI travel conversations into beautiful, 
              money-saving visual adventures.
            </p>
          </div>

          {/* Value Props */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto py-8"
          >
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">Early Access</h3>
              <p className="text-gray-600 text-sm">Get exclusive access to TravelViz before public launch</p>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <Star className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">Special Pricing</h3>
              <p className="text-gray-600 text-sm">Lock in founder pricing with exclusive discounts</p>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <Users className="h-8 w-8 text-blue-500 mx-auto mb-4" />
              <h3 className="font-semibold text-gray-900 mb-2">Shape the Product</h3>
              <p className="text-gray-600 text-sm">Influence features and provide feedback during beta</p>
            </div>
          </motion.div>

          {/* Waitlist Signup */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.5 }}
            className="max-w-lg mx-auto"
          >
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 p-8">
              <WaitlistSignup 
                size="lg"
                showBenefits={true}
                className="space-y-6"
              />
            </div>
          </motion.div>

          {/* Social Proof */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7, duration: 0.5 }}
            className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-gray-600"
          >
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>1,000+ already on waitlist</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Average $400 saved per trip</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span>30-second AI import</span>
            </div>
          </motion.div>

          {/* Timeline */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.9, duration: 0.5 }}
            className="bg-gradient-to-r from-orange-100 to-pink-100 rounded-2xl p-8 max-w-2xl mx-auto"
          >
            <div className="flex items-center justify-center space-x-2 mb-4">
              <Clock className="h-5 w-5 text-orange-600" />
              <h3 className="font-semibold text-orange-900">Launch Timeline</h3>
            </div>
            <div className="space-y-3 text-sm text-orange-800">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>✅ Core features in development</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span>🔄 Beta testing with waitlist members</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                <span>🚀 Public launch (Coming Soon!)</span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Background Decoration */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-orange-200 to-pink-200 rounded-full blur-3xl opacity-50"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-orange-200 to-pink-200 rounded-full blur-3xl opacity-50"></div>
      </div>
    </div>
  );
} 