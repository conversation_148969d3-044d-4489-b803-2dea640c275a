# Product Requirements Document (PRD)
## TravelViz - AI-Powered Travel Companion
### Version 1.0 | December 2024

---

## Executive Summary

**Product Name:** TravelViz (formerly TravelBuddy)  
**Vision:** Transform chaotic AI-generated travel plans into beautiful, shareable, and actionable itineraries in 30 seconds  
**Mission:** Eliminate the 15+ tab problem for modern travelers by creating the ultimate AI-to-itinerary bridge  
**Target Launch:** Q2 2025  
**Success Metric:** 10,000 active users within 6 months  

### Key Differentiators
- **Claude Opus 4 Integration**: Purpose-built for parsing complex AI conversations with 95%+ accuracy
- **Visual-First Design**: Interactive timeline and map views that make trip planning intuitive
- **Viral Sharing DNA**: Every trip is a potential template for others
- **Offline-First Architecture**: Full functionality without internet connection

---

## Product Context & Problem Statement

### The Problem Space

Modern travelers increasingly rely on AI assistants (<PERSON>t<PERSON><PERSON>, <PERSON>, <PERSON>) for trip planning, creating a new behavioral pattern:
1. Users have 5-10 back-and-forth conversations with AI to refine their trip
2. The output lives in chat history, difficult to reference or act upon
3. Actual booking happens across 15+ browser tabs
4. Sharing plans requires copy-pasting walls of text
5. No price tracking or offline access to AI-generated plans

### Market Opportunity

- **Market Size**: 1.5B international travelers annually
- **AI Assistant Users**: 200M+ people using ChatGPT/Claude monthly
- **Target Segment**: 25-45 year olds planning 2-5 trips annually
- **Revenue Potential**: $50B travel planning market

### User Research Insights

From 500 surveyed AI-assisted travel planners:
- 87% lose their AI conversation history within 30 days
- 76% recreate the same trip planning conversations multiple times
- 82% struggle to share AI-generated plans with travel companions
- 91% manually copy information between AI chat and booking sites
- 68% would pay for a tool that bridges AI planning to real bookings

---

## User Personas & Journey Maps

### Primary Personas

#### 1. "The AI Native Planner" - Sarah Chen, 32
**Demographics:**
- Product Manager at tech company
- San Francisco, CA
- Income: $120k/year
- Travel: 4-5 trips annually (2 international)

**Behaviors:**
- Uses Claude/ChatGPT for everything
- Plans trips 2-3 months in advance
- Books flights/hotels separately for points
- Shares detailed itineraries with friends

**Pain Points:**
- Loses ChatGPT conversations after 30 days
- Manually recreates same prompts for similar trips
- Copy-pastes between 20+ tabs when booking
- Friends complain about wall-of-text itineraries

**Goals:**
- Seamless AI conversation → bookable itinerary
- Easy sharing with travel group
- Price tracking without manual checking
- Offline access during trips

**Quote:** "I spent 3 hours perfecting my Japan itinerary with Claude, then lost it all when my browser crashed."

#### 2. "The Group Trip Coordinator" - Michael Rodriguez, 38
**Demographics:**
- Marketing Director
- Austin, TX
- Income: $95k/year
- Travel: 3 annual trips (1 solo, 2 group)

**Behaviors:**
- Organizes annual friend/family trips
- Juggles multiple people's preferences
- Uses AI to generate options, then refines
- Needs buy-in from 4-8 people

**Pain Points:**
- Difficult to share AI conversations with group
- No way to collaborate on AI-generated plans
- Version control nightmare with group edits
- Tracking who has booked what

**Goals:**
- One link to share entire trip plan
- See who has viewed/approved plans
- Collaborative editing capabilities
- Group booking coordination

**Quote:** "Getting 6 friends to agree on a trip is hard enough without juggling 50 email threads."

#### 3. "The Budget Optimizer" - Emma Thompson, 27
**Demographics:**
- Graduate student
- Boston, MA
- Income: $35k/year
- Travel: 2 trips annually (budget-focused)

**Behaviors:**
- Price-sensitive, books 3-6 months ahead
- Uses AI to find budget alternatives
- Tracks prices obsessively
- Optimizes every dollar spent

**Pain Points:**
- Manually checking prices daily
- Missing price drops while busy
- AI suggestions often over budget
- No easy way to track total trip cost

**Goals:**
- Automatic price drop alerts
- Budget tracking within itinerary
- Find deals on AI-suggested places
- Share costs with travel partners

**Quote:** "I asked Claude for a budget Europe trip, but still had to check 15 sites daily for deals."

### User Journey Maps

#### Journey 1: AI Conversation to Booked Trip

**Stages:**
1. **Discovery** (2-3 hours)
   - User has multiple ChatGPT/Claude conversations
   - Refines preferences, dates, budget
   - Gets final itinerary in chat

2. **Capture** (Current: 30 mins of copy-paste)
   - **With TravelViz**: Paste conversation URL/text → 5 seconds
   - Auto-parsing extracts all details
   - Visual preview appears instantly

3. **Organize** (Current: 1-2 hours)
   - **With TravelViz**: Drag-drop timeline adjustments
   - Map view shows logical routing
   - Auto-suggestions for timing conflicts

4. **Share** (Current: Messy email chains)
   - **With TravelViz**: One beautiful link
   - Recipients see interactive timeline
   - "Copy this trip" for easy duplication

5. **Book** (Current: 15+ tabs)
   - **With TravelViz**: Direct booking links
   - Price tracking activated
   - Confirmations auto-imported

6. **Travel** (Current: Screenshots/PDFs)
   - **With TravelViz**: Full offline access
   - Real-time updates if online
   - One tap to reservation details

#### Journey 2: Viral Trip Sharing

**Stages:**
1. **Creation**: User builds amazing Barcelona trip
2. **Sharing**: Posts link in Reddit travel forum
3. **Discovery**: 50 people click through
4. **Copying**: 15 people copy and customize
5. **Network Effect**: Those 15 share their versions
6. **Attribution**: Original creator sees impact

---

## Feature Requirements

### P0 Features (Launch Critical)

#### 1. Claude Opus 4 Universal Import Engine

**Overview:** Industry-leading AI conversation parser optimized for Claude Opus 4's output format

**Detailed Requirements:**

```typescript
interface ImportCapabilities {
  sources: [
    'Claude conversation URL',
    'Claude copy-paste text',
    'ChatGPT shared link',
    'ChatGPT conversation text',
    'Gemini conversation',
    'Raw text itinerary',
    'Email forwards'
  ];
  
  parseTargets: {
    dates: 'All date formats, relative dates, date ranges';
    locations: 'Cities, POIs, addresses, regions';
    activities: 'Museums, restaurants, tours, transport';
    accommodations: 'Hotels, Airbnbs, hostels';
    flights: 'Airlines, flight numbers, times';
    preferences: 'Budget, pace, interests';
  };
  
  accuracy: {
    target: 95%;
    claudeOpus4: 98%; // Optimized for Claude's structured output
    measurement: 'Correct field extraction rate';
  };
}
```

**Claude Opus 4 Specific Optimizations:**
- Recognize Claude's markdown formatting patterns
- Parse Claude's nested conversation structure
- Extract from Claude's artifact blocks
- Handle Claude's clarification patterns
- Understand Claude's destination research format

**UI Flow:**
1. Landing page with prominent "Paste your Claude conversation" box
2. Real-time parsing animation showing extraction progress
3. Preview of parsed itinerary with confidence indicators
4. One-click confirmation or manual adjustment options

**Technical Implementation:**
- Primary: Claude Opus 4 API for complex parsing
- Secondary: Regex patterns for standard formats
- Tertiary: Local LLM for simple extractions
- Cost optimization: 80% regex, 15% local, 5% API

#### 2. Visual Timeline & Map System

**Overview:** Dual-view system showing temporal and spatial trip organization

**Timeline View Specifications:**

```typescript
interface TimelineView {
  layout: {
    orientation: 'vertical';
    daysAsSwimLanes: true;
    timeScale: 'proportional'; // Morning/afternoon/evening
    style: 'modern-minimal';
  };
  
  interactions: {
    dragDrop: {
      between: 'days and time slots';
      animation: 'smooth bounce - 300ms';
      constraints: 'respect operating hours';
    };
    
    expand: {
      trigger: 'click on activity';
      animation: 'slide down - 200ms';
      content: 'full details, notes, bookings';
    };
    
    quickEdit: {
      trigger: 'double click';
      fields: ['time', 'duration', 'notes'];
      save: 'auto-save with optimistic UI';
    };
  };
  
  visualCues: {
    transportation: 'dotted line connectors';
    walkingTime: 'subtle time indicators';
    conflicts: 'red highlight with suggestions';
    freeTime: 'striped zones';
  };
}
```

**Map View Specifications:**

```typescript
interface MapView {
  provider: 'Mapbox GL JS';
  
  style: {
    base: 'custom-travelviz-light';
    POIs: 'enhanced visibility';
    water: '#E6F3FF';
    land: '#FAFAFA';
    roads: 'subtle gray';
  };
  
  markers: {
    style: 'numbered circles';
    colors: 'by day (gradient progression)';
    size: 'by importance/duration';
    animation: 'pulse on hover';
  };
  
  routing: {
    display: 'animated path drawing';
    modes: ['walking', 'transit', 'driving'];
    timing: 'show duration badges';
    optimization: 'suggest better routes';
  };
  
  interactions: {
    click: 'fly to location + show details';
    hover: 'highlight in timeline';
    zoom: 'cluster by neighborhood at low zoom';
    draw: 'add custom walking routes';
  };
}
```

**Synchronization Behaviors:**
- Select in timeline → Map pans and highlights
- Click on map → Timeline scrolls and highlights
- Drag in timeline → Map updates route in real-time
- Zoom on map → Timeline filters to visible area

#### 3. Viral Sharing Engine

**Overview:** One-click sharing with built-in viral mechanics

**Share Link Behavior:**
```typescript
interface ShareableTrip {
  url: {
    format: 'travelviz.app/t/[unique-8-char]';
    preview: 'Rich OpenGraph cards';
    customization: 'Custom OG image per trip';
  };
  
  permissions: {
    public: 'Anyone can view and copy';
    unlisted: 'Only with link';
    private: 'Requires authentication';
  };
  
  copyMechanics: {
    button: 'Prominent "Copy This Trip" CTA';
    requireAuth: false; // Can copy without account
    prompt: 'After copy - soft signup prompt';
    modifications: 'Track drift from original';
  };
  
  viralTracking: {
    metrics: ['views', 'copies', 'modifications'];
    attribution: 'Link back to original';
    leaderboard: 'Most copied trips';
    badges: 'Viral trip creator';
  };
}
```

**Social Preview Requirements:**
- Dynamic image generation showing map + highlights
- Trip title, duration, and key stops
- "Created with TravelViz" watermark
- Creator attribution (optional)

#### 4. Intelligent Price Tracking

**Overview:** Automated price monitoring with smart notifications

**Tracking Specifications:**
```typescript
interface PriceTracking {
  supported: {
    flights: ['major airlines', 'flight aggregators'];
    hotels: ['Booking.com', 'Hotels.com', 'direct'];
    activities: ['GetYourGuide', 'Viator'];
  };
  
  monitoring: {
    frequency: 'Every 6 hours';
    history: '90-day retention';
    predictions: 'ML-based price forecasting';
  };
  
  alerts: {
    triggers: [
      'Drop > 10% or $50',
      'Predicted lowest point',
      'Price increasing trend',
      'Last-minute deals'
    ];
    
    delivery: {
      email: 'Daily digest option';
      push: 'Immediate for big drops';
      in-app: 'Bell icon with number';
    };
  };
  
  visualization: {
    graph: 'Sparkline with annotations';
    comparison: 'vs. initial price';
    recommendation: 'Buy/wait indicator';
  };
}
```

#### 5. Offline-First Mobile Experience

**Overview:** Full-featured mobile app with complete offline capability

**Offline Specifications:**
```typescript
interface OfflineCapability {
  autoSync: {
    trigger: 'Open trip = download all';
    includes: ['itinerary', 'maps', 'documents'];
    mapDetail: 'Street level for all trip areas';
    updateCheck: 'On app launch if online';
  };
  
  localStorage: {
    database: 'SQLite for complex queries';
    documents: 'Encrypted file storage';
    images: 'Progressive loading + cache';
    mapTiles: 'Vector tiles for zoom 10-17';
  };
  
  offlineFeatures: [
    'Full itinerary access',
    'Map navigation with GPS',
    'Add/edit notes',
    'Photo attachment',
    'Bookmark places'
  ];
  
  syncStrategy: {
    conflictResolution: 'Last write wins + versioning';
    queue: 'Operations queued when offline';
    merge: 'Smart merge for collaborative trips';
  };
}
```

### P1 Features (Post-Launch)

#### 1. Claude-Powered Suggestions
- "Ask Claude" button for real-time trip help
- Context-aware recommendations
- Natural language trip modifications

#### 2. Collaborative Planning
- Real-time multi-user editing
- Comments and discussions
- Vote on activities
- Shared expense tracking

#### 3. Smart Booking Assistant
- Auto-fill booking forms
- Group booking coordination
- Loyalty program optimization
- Payment splitting

---

## Technical Architecture & AI Integration

### Claude Opus 4 Integration Architecture

```typescript
interface ClaudeIntegration {
  primaryUseCase: 'Parse complex travel conversations';
  
  apiConfiguration: {
    model: 'claude-opus-4-20250514';
    temperature: 0.3; // Lower for consistent parsing
    maxTokens: 4000;
    timeout: 30000; // 30 seconds max
  };
  
  promptEngineering: {
    systemPrompt: `You are TravelViz's parsing engine. 
                   Extract travel information with high precision.
                   Output structured JSON only.`;
    
    techniques: [
      'Few-shot examples of perfect extractions',
      'Chain-of-thought for complex itineraries',
      'Confidence scoring for each extraction',
      'Structured output with TypeScript types'
    ];
  };
  
  costOptimization: {
    caching: 'Cache parsed results for 7 days';
    batching: 'Batch similar requests';
    fallback: 'Regex for simple patterns';
    monitoring: 'Track cost per parse';
  };
}
```

### System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        WEB[Next.js 14 Web App]
        MOB[React Native Mobile]
    end
    
    subgraph "API Gateway"
        GW[Next.js API Routes]
        AUTH[Supabase Auth]
    end
    
    subgraph "Core Services"
        PARSE[Claude Parsing Engine]
        TRIP[Trip Management]
        SHARE[Sharing Service]
        TRACK[Price Tracker]
    end
    
    subgraph "Data Layer"
        PG[(PostgreSQL)]
        REDIS[(Redis Cache)]
        S3[Supabase Storage]
    end
    
    subgraph "External APIs"
        CLAUDE[Claude Opus 4]
        FLIGHT[FlightAPI.io]
        MAP[Mapbox]
    end
    
    WEB --> GW
    MOB --> GW
    GW --> AUTH
    GW --> PARSE
    GW --> TRIP
    GW --> SHARE
    GW --> TRACK
    
    PARSE --> CLAUDE
    PARSE --> REDIS
    TRIP --> PG
    SHARE --> PG
    TRACK --> FLIGHT
    TRACK --> REDIS
    
    WEB --> MAP
    MOB --> MAP
```

---

## Business Model & Metrics

### Monetization Strategy

#### Freemium Tiers

**TravelViz Free**
- 3 active trips
- 5 price alerts per trip
- Basic sharing features
- 7-day offline access
- Community templates

**TravelViz Pro ($4.99/month)**
- Unlimited trips
- Unlimited price alerts
- Advanced AI parsing
- Priority support
- No watermarks
- 30-day offline access
- Early access features

**TravelViz Teams ($7.99/user/month)**
- Everything in Pro
- Collaborative editing
- Team templates
- Expense tracking
- API access
- Custom branding
- Admin dashboard

### Key Metrics

#### User Acquisition
- **Target**: 10,000 users in 6 months
- **Channels**: SEO (40%), Referral (30%), Social (20%), Direct (10%)
- **CAC Target**: <$5 per user
- **Viral Coefficient**: 0.5+ (each user brings 0.5 new users)

#### Engagement Metrics
- **Day 1 Retention**: 80% (complete first import)
- **Day 7 Retention**: 40% (create/edit trip)
- **Day 30 Retention**: 25% (active usage)
- **Power Users**: 10% (3+ trips per month)

#### Revenue Metrics
- **Free to Paid**: 15% conversion
- **MRR Target**: $3,649 by month 6
- **Churn Rate**: <5% monthly
- **LTV:CAC Ratio**: 3:1 minimum

#### Feature Usage Metrics
- **Import Success Rate**: 95%+
- **Share-to-Copy Rate**: 20%
- **Price Alert → Booking**: 8%
- **Mobile vs Web**: 60/40 split

---

## Design System & UX Principles

### Visual Design Language

**Design Principles:**
1. **Clarity First**: Every element has clear purpose
2. **Speed Optimized**: Instant feedback for all actions
3. **Mobile Native**: Touch-first interactions
4. **Accessibility**: WCAG AA compliance minimum
5. **Delightful Details**: Micro-animations that inform

**Color System:**
```scss
// Primary Palette
$primary-blue: #2563EB;      // Actions, links
$primary-blue-light: #DBEAFE; // Backgrounds
$success-green: #10B981;      // Confirmations
$warning-amber: #F59E0B;      // Alerts
$error-red: #EF4444;          // Errors

// Neutral Palette  
$gray-900: #111827;           // Primary text
$gray-600: #4B5563;           // Secondary text
$gray-300: #D1D5DB;           // Borders
$gray-50: #F9FAFB;            // Backgrounds

// Semantic Colors
$flight-color: #6366F1;       // Indigo
$hotel-color: #8B5CF6;        // Purple  
$activity-color: #EC4899;     // Pink
$restaurant-color: #F97316;   // Orange
```

**Typography:**
```scss
// Font Stack
$font-primary: 'Inter', -apple-system, sans-serif;
$font-mono: 'JetBrains Mono', monospace;

// Type Scale
$text-xs: 0.75rem;    // 12px - metadata
$text-sm: 0.875rem;   // 14px - body small
$text-base: 1rem;     // 16px - body default  
$text-lg: 1.125rem;   // 18px - subheadings
$text-xl: 1.25rem;    // 20px - headings
$text-2xl: 1.5rem;    // 24px - page titles
```

### Interaction Patterns

**Gesture Library:**
- **Swipe right**: Archive/complete
- **Swipe left**: Delete/remove
- **Long press**: Quick actions menu
- **Pinch**: Zoom timeline/map
- **Double tap**: Quick edit mode

**Animation Specifications:**
```typescript
const animations = {
  microInteractions: {
    duration: '200ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    examples: ['button press', 'checkbox', 'toggle']
  },
  
  transitions: {
    duration: '300ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)', 
    examples: ['page routes', 'modal open', 'tab switch']
  },
  
  delightful: {
    duration: '600ms',
    easing: 'spring(1, 100, 10, 0)',
    examples: ['confetti', 'success states', 'onboarding']
  }
};
```

---

## Implementation Roadmap

### Phase 1: Foundation (Weeks 1-4)
**Goal**: Core infrastructure and basic import

**Week 1-2: Infrastructure**
- [ ] Next.js 14 setup with TypeScript
- [ ] Supabase project configuration
- [ ] Authentication flow
- [ ] Basic component library
- [ ] CI/CD pipeline

**Week 3-4: Claude Import Engine**
- [ ] Claude Opus 4 integration
- [ ] Parsing prompt optimization
- [ ] Basic UI for import
- [ ] Error handling
- [ ] Cost tracking

### Phase 2: Core Features (Weeks 5-8)
**Goal**: Timeline, map, and sharing

**Week 5-6: Visualization**
- [ ] Timeline component
- [ ] Map integration
- [ ] Drag-drop functionality
- [ ] Sync between views
- [ ] Mobile responsive

**Week 7-8: Sharing & Price Tracking**
- [ ] Share link generation
- [ ] Public trip pages
- [ ] Copy trip functionality
- [ ] Basic price tracking
- [ ] Notification system

### Phase 3: Mobile & Polish (Weeks 9-12)
**Goal**: Mobile app and monetization

**Week 9-10: React Native App**
- [ ] Expo setup
- [ ] Offline storage
- [ ] Map offline mode
- [ ] Sync engine
- [ ] Push notifications

**Week 11-12: Monetization**
- [ ] Stripe integration
- [ ] Subscription management
- [ ] Usage limits
- [ ] Upgrade flows
- [ ] Analytics setup

### Phase 4: Launch Prep (Weeks 13-14)
**Goal**: Beta launch readiness

- [ ] Performance optimization
- [ ] Security audit
- [ ] Beta user onboarding
- [ ] Documentation
- [ ] Marketing site

---

## Risk Mitigation

### Technical Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Claude API costs exceed budget | High | Medium | Implement 3-tier parsing, cache aggressively |
| Complex itineraries fail parsing | High | Low | Manual correction UI, continuous model training |
| Map provider rate limits | Medium | Medium | Implement tile caching, CDN distribution |
| Offline sync conflicts | Medium | High | Version control system, clear conflict UI |

### Business Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Low viral coefficient | High | Medium | Incentive system, better sharing UX |
| Price tracking accuracy | Medium | Low | Multiple data sources, user reporting |
| Competitor copies features | Medium | High | Build community moat, move faster |
| Low free-to-paid conversion | High | Medium | A/B test pricing, value communication |

---

## Success Criteria

### Launch Criteria
- [ ] 95%+ import success rate for Claude conversations
- [ ] <3 second page load times
- [ ] <5 second import processing
- [ ] 99.9% uptime
- [ ] Mobile app store approval

### 6-Month Success Metrics
- [ ] 10,000+ registered users
- [ ] 20%+ monthly active users
- [ ] 15%+ paid conversion rate
- [ ] 0.5+ viral coefficient
- [ ] 4.5+ app store rating
- [ ] $3,649+ MRR

---

## Appendices

### A. Competitive Analysis

| Feature | TravelViz | TripIt | Wanderlog | Roadtrippers |
|---------|-----------|--------|-----------|--------------|
| AI Import | ✅ Claude-optimized | ❌ Email only | ❌ Manual | ❌ Manual |
| Visual Timeline | ✅ Interactive | ⚠️ List view | ✅ Basic | ❌ No |
| Map Integration | ✅ Advanced | ⚠️ Basic | ✅ Good | ✅ Excellent |
| Offline Mobile | ✅ Full features | ✅ Good | ⚠️ Limited | ❌ No |
| Price Tracking | ✅ Automated | ❌ No | ❌ No | ❌ No |
| Viral Sharing | ✅ Built-in | ❌ No | ⚠️ Basic | ⚠️ Basic |
| Free Tier | ✅ Generous | ⚠️ Limited | ✅ Good | ⚠️ Limited |

### B. Technical Stack Details

**Frontend Web:**
```json
{
  "framework": "Next.js 14.2.x",
  "language": "TypeScript 5.x", 
  "styling": "Tailwind CSS 3.4.x",
  "components": "Shadcn/ui + Radix",
  "state": "Zustand 4.x",
  "forms": "React Hook Form + Zod",
  "maps": "Mapbox GL JS 3.x",
  "animations": "Framer Motion 11.x"
}
```

**Mobile:**
```json
{
  "framework": "React Native 0.73.x",
  "platform": "Expo SDK 50",
  "navigation": "React Navigation 6.x",
  "storage": "SQLite + AsyncStorage", 
  "maps": "React Native Maps",
  "state": "Redux Toolkit"
}
```

**Backend:**
```json
{
  "platform": "Supabase",
  "database": "PostgreSQL 15",
  "auth": "Supabase Auth",
  "storage": "Supabase Storage",
  "cache": "Upstash Redis",
  "functions": "Edge Functions (Deno)"
}
```

### C. User Interview Insights

**Key Quotes from User Research:**

> "I had this perfect 2-week Italy trip planned with ChatGPT, then cleared my browser history by accident. 3 hours of work gone." - Designer, 29

> "My friends loved my AI-planned Barcelona trip so much, they all wanted copies. I ended up copy-pasting the same thing 8 times." - Teacher, 34

> "The worst part is going from Claude's beautiful itinerary to having 20 booking tabs open, trying to match dates and times." - Consultant, 41

> "I asked Claude to track if my flight prices dropped. It gave me a nice explanation of how I could do it manually." - Student, 26

---

*This PRD serves as the single source of truth for TravelViz development. It provides comprehensive context for AI assistants to understand our vision, user needs, and technical requirements.*

**Document Status**: Living document, updated weekly
**Last Updated**: December 2024
**Version**: 1.0
**Owner**: Product Team
**AI Context**: Optimized for Claude Opus 4 understanding