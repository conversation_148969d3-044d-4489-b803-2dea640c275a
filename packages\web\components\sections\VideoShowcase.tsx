"use client";

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Pause, Volume2, VolumeX, ArrowDown, Sparkles, MapPin, Calendar, Bot, Share2, Upload, Download, Users, Clock, Star, Heart, Eye, MessageCircle, Copy, ExternalLink, Filter, Search, Grid, List, DollarSign, Bell, TrendingDown, AlertTriangle, CreditCard, PiggyBank } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const showcaseSegments = [
  {
    id: 1,
    title: "Import AI Travel Plans",
    subtitle: "From ChatGPT to Beautiful Itineraries",
    description: "Paste any AI-generated travel plan and watch it transform into an organized, visual journey with automatic budget extraction",
    duration: 6000,
    visual: {
      type: "text-to-visual",
      content: "AI import transformation"
    }
  },
  {
    id: 2,
    title: "Smart Money Features",
    subtitle: "Automatic Price Alerts & Budget Tracking",
    description: "Get automatic price alerts, budget tracking, and cost sharing with your group. Save $400+ per trip with intelligent money management",
    duration: 6000,
    visual: {
      type: "money-features",
      content: "Smart money management"
    }
  },
  {
    id: 3,
    title: "Visual Timeline",
    subtitle: "Beautiful Interactive Timeline with Budget Tracking",
    description: "See your trip organized in a beautiful, interactive timeline with real-time budget tracking, cost optimization, and smart scheduling",
    duration: 6000,
    visual: {
      type: "timeline",
      content: "Enhanced timeline with budget"
    }
  },
  {
    id: 4,
    title: "Collaborate & Share",
    subtitle: "Plan Together, Split Costs Seamlessly",
    description: "Share itineraries with friends, track group expenses, split costs automatically, and collaborate on the perfect budget-friendly trip",
    duration: 6000,
    visual: {
      type: "sharing",
      content: "Group collaboration with cost sharing"
    }
  },
  {
    id: 5,
    title: "Export Everywhere",
    subtitle: "Your Plans, Your Way, Your Budget",
    description: "Export to PDF with budget breakdown, sync to calendar with cost alerts, or share budget-optimized plans anywhere",
    duration: 6000,
    visual: {
      type: "export",
      content: "Export with budget features"
    }
  }
];

export function VideoShowcase() {
  const [currentSegment, setCurrentSegment] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isMuted, setIsMuted] = useState(true);
  const [progress, setProgress] = useState(0);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Auto-play functionality with faster updates
  useEffect(() => {
    if (!isPlaying) return;

    const segment = showcaseSegments[currentSegment];
    const startTime = Date.now();

    const updateProgress = () => {
      const elapsed = Date.now() - startTime;
      const segmentProgress = Math.min((elapsed / segment.duration) * 100, 100);
      setProgress(segmentProgress);

      if (segmentProgress >= 100) {
        setCurrentSegment((prev) => (prev + 1) % showcaseSegments.length);
        setProgress(0);
      }
    };

    intervalRef.current = setInterval(updateProgress, 30); // Reduced from 50ms for smoother progress

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [currentSegment, isPlaying]);

  const handleUserInteraction = () => {
    if (!hasUserInteracted) {
      setHasUserInteracted(true);
      setIsPlaying(true);
    }
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
    handleUserInteraction();
  };

  const goToSegment = (index: number) => {
    setCurrentSegment(index);
    setProgress(0);
    handleUserInteraction();
  };

  const currentSegmentData = showcaseSegments[currentSegment];

  return (
    <div className="relative w-full bg-white rounded-xl sm:rounded-2xl lg:rounded-3xl overflow-hidden shadow-xl sm:shadow-2xl border-2 border-gray-200 sm:border border-gray-100">
      {/* Progress Indicator */}
      <div className="absolute top-0 left-0 right-0 z-20">
        <div className="flex">
          {showcaseSegments.map((_, index) => (
            <div key={index} className="flex-1 h-1.5 sm:h-1 bg-gray-200">
              <motion.div 
                className={`h-full bg-gradient-to-r from-orange-500 to-pink-500 transition-all duration-75 ${
                  index === currentSegment ? 'opacity-100' : index < currentSegment ? 'opacity-100' : 'opacity-0'
                }`}
                style={{
                  width: index === currentSegment ? `${progress}%` : index < currentSegment ? '100%' : '0%'
                }}
                initial={{ width: 0 }}
                animate={{ 
                  width: index === currentSegment ? `${progress}%` : index < currentSegment ? '100%' : '0%'
                }}
                transition={{ duration: 0.1, ease: "linear" }}
              />
            </div>
          ))}
        </div>
      </div>

      {/* Video Controls */}
      <div className="absolute top-3 sm:top-6 right-3 sm:right-6 z-20 flex items-center space-x-2">
        <motion.button
          onClick={togglePlayPause}
          className="p-2.5 sm:p-3 bg-white/80 backdrop-blur-sm shadow-xl rounded-full text-gray-700 hover:bg-white transition-all duration-200 hover:scale-105 border-2 border-gray-200 touch-spacing"
          aria-label={isPlaying ? "Pause" : "Play"}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <motion.div
            initial={false}
            animate={{ rotate: isPlaying ? 0 : 0 }}
            transition={{ duration: 0.2 }}
          >
            {isPlaying ? <Pause className="h-4 w-4 sm:h-4 sm:w-4" /> : <Play className="h-4 w-4 sm:h-4 sm:w-4" />}
          </motion.div>
        </motion.button>
      </div>

      {/* Main Content */}
      <div className="relative bg-gradient-to-br from-gray-50 via-white to-blue-50/30 min-h-[700px] sm:min-h-[750px] md:min-h-[600px] lg:min-h-[750px] overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center p-4 sm:p-6 md:p-8 lg:p-12">
          <div className="w-full max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row gap-8 lg:gap-16 items-center">
              {/* Text Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentSegment}
                  initial={{ opacity: 0, x: -50, y: 20 }}
                  animate={{ opacity: 1, x: 0, y: 0 }}
                  exit={{ opacity: 0, x: 50, y: -20 }}
                  transition={{ duration: 0.4, ease: "easeOut" }}
                  className="space-y-4 sm:space-y-6 text-left w-full order-2 lg:order-1"
                >
                  <div className="space-y-3 sm:space-y-4">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1, duration: 0.3 }}
                      className="inline-flex items-center space-x-2 bg-gradient-to-r from-orange-100 to-pink-100 rounded-full px-3 py-1.5 text-orange-600 text-xs sm:text-sm font-medium border border-orange-200"
                    >
                      <Sparkles className="h-4 w-4" />
                      <span>Step {currentSegment + 1} of {showcaseSegments.length}</span>
                    </motion.div>
                    
                    <motion.h2
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2, duration: 0.3 }}
                      className="text-3xl sm:text-4xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight"
                    >
                      {currentSegmentData.title}
                    </motion.h2>
                    
                    <motion.p
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3, duration: 0.3 }}
                      className="text-base sm:text-lg lg:text-xl text-orange-600 font-semibold"
                    >
                      {currentSegmentData.subtitle}
                    </motion.p>
                  </div>
                  
                  <motion.p
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4, duration: 0.3 }}
                    className="text-base sm:text-base lg:text-lg text-gray-700 leading-relaxed max-w-xl font-medium"
                  >
                    {currentSegmentData.description}
                  </motion.p>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5, duration: 0.3 }}
                    className="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 pt-2"
                  >
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button 
                        className="bg-gradient-to-r from-orange-500 to-pink-500 hover:from-orange-600 hover:to-pink-600 text-white px-6 py-3 rounded-xl font-semibold text-base shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        onClick={handleUserInteraction}
                      >
                        <Sparkles className="h-5 w-5 mr-2" />
                        Try It Free
                      </Button>
                    </motion.div>
                    <motion.button
                      onClick={() => setIsPlaying(!isPlaying)}
                      className="text-gray-600 hover:text-gray-800 text-sm font-medium transition-colors flex items-center space-x-2 group"
                      whileHover={{ scale: 1.02 }}
                    >
                      <motion.div
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.2 }}
                      >
                        {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                      </motion.div>
                      <span>{isPlaying ? 'Pause Demo' : 'Resume Demo'}</span>
                    </motion.button>
                  </motion.div>
                </motion.div>
              </AnimatePresence>

              {/* Visual Content */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={`visual-${currentSegment}`}
                  initial={{ opacity: 0, scale: 0.9, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.9, y: -20 }}
                  transition={{ duration: 0.5, ease: "easeOut" }}
                  className="relative w-full flex items-center justify-center order-1 lg:order-2 h-auto aspect-[4/3] sm:aspect-video lg:h-[600px] lg:aspect-auto"
                >
                  <div className="w-full max-w-sm sm:max-w-md lg:max-w-lg">
                    <VisualContent segment={currentSegmentData} isPlaying={isPlaying} />
                  </div>
                </motion.div>
              </AnimatePresence>
            </div>
          </div>
        </div>

        {/* Navigation Dots */}
        <div className="absolute bottom-6 left-1/2 -translate-x-1/2 z-20 flex items-center space-x-2">
          {showcaseSegments.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSegment(index)}
              className={`w-1.5 h-1.5 sm:w-2 sm:h-2 lg:w-2.5 lg:h-2.5 rounded-full transition-all duration-300 ${
                currentSegment === index ? 'bg-gradient-to-r from-orange-500 to-pink-500 scale-125 sm:scale-150' : 'bg-gray-300 hover:bg-gray-400'
              }`}
              aria-label={`Go to step ${index + 1}`}
            />
          ))}
        </div>
        
        <div className="absolute bottom-16 sm:bottom-16 left-1/2 -translate-x-1/2 z-10 text-center text-gray-500 text-xs font-medium hidden lg:flex items-center space-x-2">
          <ArrowDown className="w-3 h-3 animate-bounce" />
          <span>Scroll for more</span>
        </div>
      </div>
    </div>
  );
}

// Enhanced Visual Content Component
interface VisualContentProps {
  segment: typeof showcaseSegments[0];
  isPlaying: boolean;
}

function VisualContent({ segment, isPlaying }: VisualContentProps): JSX.Element {
  const [animationStep, setAnimationStep] = useState(0);

  useEffect(() => {
    if (!isPlaying) return;

    const interval = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 6);
    }, 1200); // Reduced from 1800ms for faster animations

    return () => clearInterval(interval);
  }, [isPlaying, segment.id]);

  const renderVisual = () => {
    switch (segment.visual.type) {
      case 'text-to-visual':
        return <TextToVisualDemo animationStep={animationStep} />;
      case 'money-features':
        return <MoneyFeaturesDemo animationStep={animationStep} />;
      case 'timeline':
        return <TimelineDemo animationStep={animationStep} />;
      case 'sharing':
        return <SharingDemo animationStep={animationStep} />;
      case 'export':
        return <ExportDemo animationStep={animationStep} />;
      default:
        return <div className="w-full h-96 bg-gray-100 rounded-xl" />;
    }
  };

  return (
    <div className="relative w-full">
      <motion.div 
        className="bg-white rounded-xl sm:rounded-2xl shadow-2xl border-2 border-gray-200 overflow-hidden"
        whileHover={{ scale: 1.02, rotateY: 2 }}
        transition={{ duration: 0.3 }}
      >
        {renderVisual()}
      </motion.div>
    </div>
  );
}

// Enhanced Demo Components with faster animations
interface DemoProps {
  animationStep: number;
}

function TextToVisualDemo({ animationStep }: DemoProps): JSX.Element {
  return (
    <div className="p-3 sm:p-4 lg:p-6 space-y-3 sm:space-y-4 lg:space-y-6 h-[300px] sm:h-[350px] lg:h-[450px] bg-gradient-to-br from-gray-50 to-white">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Bot className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500" />
          <span className="text-xs sm:text-sm font-medium text-gray-700">AI Import</span>
        </div>
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: animationStep >= 1 ? 1 : 0 }}
          transition={{ duration: 0.3, type: "spring" }}
        >
          <Badge variant="outline" className="text-xs">Processing</Badge>
        </motion.div>
      </div>

      {/* Input Section */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: animationStep >= 1 ? 1 : 0.4, y: 0 }}
        transition={{ duration: 0.3 }}
        className="space-y-2 sm:space-y-3"
      >
        <div className="bg-gray-900 rounded-lg p-3 sm:p-4 border border-gray-700">
          <div className="flex items-center space-x-2 mb-2">
            <motion.div 
              className="w-2 h-2 sm:w-3 sm:h-3 bg-green-400 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity }}
            />
            <span className="text-green-400 text-xs font-mono">ChatGPT Output</span>
          </div>
          <div className="text-xs sm:text-sm text-green-300 font-mono leading-relaxed">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: animationStep >= 1 ? "100%" : 0 }}
              transition={{ duration: 1.5, ease: "easeOut" }}
              className="overflow-hidden"
            >
              Day 1: Tokyo Arrival<br/>
              • 14:00 - Arrive at Haneda Airport<br/>
              • 16:00 - Check into Shibuya hotel<br/>
              <span className="hidden sm:inline">• 18:00 - Shibuya Crossing experience<br/>
              • 20:00 - Traditional izakaya dinner</span>
            </motion.div>
          </div>
        </div>
      </motion.div>
      
      {/* Processing Animation */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ 
          scale: animationStep >= 2 ? 1 : 0,
          opacity: animationStep >= 2 ? 1 : 0
        }}
        transition={{ duration: 0.4, type: "spring" }}
        className="text-center py-2 sm:py-4"
      >
        <div className="inline-flex items-center space-x-2 sm:space-x-3 bg-blue-50 rounded-full px-3 sm:px-4 py-1.5 sm:py-2 border border-blue-200">
          <motion.div 
            className="w-3 h-3 sm:w-4 sm:h-4 bg-blue-500 rounded-full"
            animate={{ scale: [1, 1.3, 1] }}
            transition={{ duration: 0.8, repeat: Infinity }}
          />
          <span className="text-xs sm:text-sm font-medium text-blue-700">AI Processing...</span>
        </div>
      </motion.div>

      {/* Output Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ 
          opacity: animationStep >= 3 ? 1 : 0,
          y: animationStep >= 3 ? 0 : 20
        }}
        transition={{ duration: 0.4 }}
        className="space-y-2 sm:space-y-3"
      >
        <div className="flex items-center space-x-2">
          <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-orange-500" />
          <span className="text-xs sm:text-sm font-medium text-gray-700">Visual Timeline</span>
        </div>
        <div className="space-y-1.5 sm:space-y-2 max-h-32 sm:max-h-40 overflow-y-auto">
          {[
            { time: '14:00', title: 'Haneda Airport', color: 'bg-purple-500', category: 'Transport' },
            { time: '16:00', title: 'Shibuya Hotel', color: 'bg-green-500', category: 'Hotel' },
            { time: '18:00', title: 'Shibuya Crossing', color: 'bg-blue-500', category: 'Activity' },
            { time: '20:00', title: 'Izakaya Dinner', color: 'bg-red-500', category: 'Restaurant' }
          ].map((item, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ 
                opacity: animationStep >= 4 ? 1 : 0,
                x: animationStep >= 4 ? 0 : -20
              }}
              transition={{ delay: index * 0.1, duration: 0.3 }}
              className={`flex items-center space-x-2 sm:space-x-3 p-2 sm:p-3 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-shadow ${
                index >= 2 ? 'hidden sm:flex' : ''
              }`}
            >
              <motion.div 
                className={`w-3 h-3 sm:w-4 sm:h-4 ${item.color} rounded-full flex-shrink-0`}
                whileHover={{ scale: 1.2 }}
              />
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <span className="text-xs sm:text-sm font-medium text-gray-900 truncate">{item.title}</span>
                  <span className="text-xs text-gray-500 ml-2">{item.time}</span>
                </div>
                <span className="text-xs text-gray-500">{item.category}</span>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}

function MoneyFeaturesDemo({ animationStep }: DemoProps): JSX.Element {
  return (
    <div className="p-3 sm:p-4 lg:p-6 space-y-3 sm:space-y-4 lg:space-y-6 h-[300px] sm:h-[350px] lg:h-[450px] bg-gradient-to-br from-green-50 via-white to-blue-50">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
          <span className="text-xs sm:text-sm font-medium text-gray-700">Smart Money Features</span>
        </div>
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: animationStep >= 1 ? 1 : 0 }}
          transition={{ duration: 0.3, type: "spring" }}
        >
          <Badge className="bg-green-500 text-white text-xs">$153 Saved</Badge>
        </motion.div>
      </div>

      {/* Price Alerts Section */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: animationStep >= 1 ? 1 : 0, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-white rounded-xl p-3 sm:p-4 border border-green-200 shadow-sm"
      >
        <div className="flex items-center space-x-2 mb-3">
          <Bell className="h-4 w-4 text-orange-500" />
          <span className="text-sm font-semibold text-gray-900">Price Alerts Active</span>
        </div>
        <div className="space-y-2">
          {[
            { name: 'Shibuya Hotel', original: '$135', current: '$120', saved: '$15', status: 'active' },
            { name: 'Flight to Tokyo', original: '$420', current: 'Tracking...', saved: '', status: 'tracking' },
            { name: 'TeamLab Tickets', original: '$32', current: 'Tracking...', saved: '', status: 'tracking' }
          ].map((item, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: -20 }}
              animate={{ 
                opacity: animationStep >= 2 ? 1 : 0,
                x: animationStep >= 2 ? 0 : -20
              }}
              transition={{ delay: index * 0.1, duration: 0.3 }}
              className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
            >
              <span className="text-xs text-gray-700">{item.name}</span>
              <div className="flex items-center space-x-1">
                {item.status === 'active' ? (
                  <>
                    <span className="text-xs line-through text-gray-400">{item.original}</span>
                    <span className="text-xs font-bold text-green-600">{item.current}</span>
                    <Badge className="bg-green-500 text-white text-xs">-{item.saved}</Badge>
                  </>
                ) : (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    >
                      <Bell className="h-3 w-3 text-orange-500" />
                    </motion.div>
                    <span className="text-xs text-gray-500">{item.current}</span>
                  </>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Budget Tracking */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: animationStep >= 3 ? 1 : 0, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-white rounded-xl p-4 border border-blue-200 shadow-sm"
      >
        <div className="flex items-center space-x-2 mb-3">
          <PiggyBank className="h-4 w-4 text-blue-600" />
          <span className="text-sm font-semibold text-gray-900">Budget Tracking</span>
        </div>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-600">Total Budget</span>
            <span className="text-sm font-bold text-gray-900">$2,000</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-gray-600">Projected Cost</span>
            <span className="text-sm font-bold text-gray-900">$1,847</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-xs text-green-600 font-medium">Under Budget</span>
            <span className="text-sm font-bold text-green-600">$153</span>
          </div>
          <motion.div
            initial={{ width: 0 }}
            animate={{ width: animationStep >= 4 ? "92%" : 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            className="h-2 bg-green-500 rounded-full relative overflow-hidden"
          >
            <motion.div
              animate={{ x: ["-100%", "100%"] }}
              transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            />
          </motion.div>
          <div className="text-xs text-gray-500 text-center">92% of budget used</div>
        </div>
      </motion.div>

      {/* Group Cost Sharing */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: animationStep >= 5 ? 1 : 0, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-white rounded-xl p-4 border border-purple-200 shadow-sm"
      >
        <div className="flex items-center space-x-2 mb-3">
          <Users className="h-4 w-4 text-purple-600" />
          <span className="text-sm font-semibold text-gray-900">Group Cost Sharing</span>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex -space-x-2">
            {['bg-blue-500', 'bg-green-500', 'bg-yellow-500'].map((color, index) => (
              <motion.div
                key={index}
                initial={{ scale: 0 }}
                animate={{ scale: animationStep >= 5 ? 1 : 0 }}
                transition={{ delay: index * 0.1, duration: 0.3, type: "spring" }}
                className={`w-6 h-6 ${color} rounded-full border-2 border-white flex items-center justify-center text-white text-xs font-bold`}
              >
                {index + 1}
              </motion.div>
            ))}
          </div>
          <span className="text-xs text-gray-600">$615 per person</span>
        </div>
      </motion.div>
    </div>
  );
}

function MapAnimationDemo({ animationStep }: DemoProps): JSX.Element {
  return (
    <div className="relative h-[450px] bg-gradient-to-br from-blue-50 via-green-50 to-blue-50">
      {/* Map Header */}
      <div className="absolute top-4 left-4 right-4 z-10">
        <motion.div 
          className="bg-white rounded-xl shadow-lg p-4 border border-gray-200"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.4 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <MapPin className="h-5 w-5 text-orange-500" />
              <div>
                <h3 className="text-sm font-semibold text-gray-900">Tokyo, Japan</h3>
                <p className="text-xs text-gray-500">Day 1 Itinerary</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className="text-xs">4 stops</Badge>
              <div className="flex space-x-1">
                <button className="p-1 hover:bg-gray-100 rounded">
                  <Grid className="h-3 w-3 text-gray-400" />
                </button>
                <button className="p-1 hover:bg-gray-100 rounded">
                  <List className="h-3 w-3 text-gray-400" />
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Animated Markers */}
      {[
        { x: '20%', y: '35%', color: 'bg-purple-500', label: '1', name: 'Haneda Airport', time: '14:00' },
        { x: '50%', y: '25%', color: 'bg-green-500', label: '2', name: 'Shibuya Hotel', time: '16:00' },
        { x: '35%', y: '55%', color: 'bg-blue-500', label: '3', name: 'Shibuya Crossing', time: '18:00' },
        { x: '60%', y: '70%', color: 'bg-red-500', label: '4', name: 'Izakaya Restaurant', time: '20:00' }
      ].map((marker, index) => (
        <motion.div
          key={index}
          initial={{ scale: 0, opacity: 0 }}
          animate={{ 
            scale: animationStep >= index + 1 ? 1 : 0,
            opacity: animationStep >= index + 1 ? 1 : 0
          }}
          transition={{ delay: index * 0.3, duration: 0.4, type: "spring", stiffness: 300 }}
          className="absolute"
          style={{ left: marker.x, top: marker.y }}
        >
          <motion.div 
            className={`w-10 h-10 ${marker.color} rounded-full shadow-lg flex items-center justify-center text-white text-sm font-bold cursor-pointer border-2 border-white`}
            whileHover={{ scale: 1.2, rotate: 5 }}
            animate={{ 
              y: [0, -5, 0],
              boxShadow: ["0 4px 6px rgba(0,0,0,0.1)", "0 8px 15px rgba(0,0,0,0.2)", "0 4px 6px rgba(0,0,0,0.1)"]
            }}
            transition={{ 
              y: { duration: 2, repeat: Infinity, ease: "easeInOut" },
              boxShadow: { duration: 2, repeat: Infinity, ease: "easeInOut" }
            }}
          >
            {marker.label}
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ delay: (index * 0.3) + 0.2, duration: 0.3 }}
            className="absolute top-12 left-1/2 transform -translate-x-1/2 bg-white rounded-lg shadow-lg p-3 text-xs text-gray-700 whitespace-nowrap border border-gray-200"
          >
            <div className="font-medium">{marker.name}</div>
            <div className="text-gray-500">{marker.time}</div>
          </motion.div>
        </motion.div>
      ))}

      {/* Route Lines with Animation */}
      <svg className="absolute inset-0 w-full h-full pointer-events-none">
        <defs>
          <linearGradient id="routeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stopColor="#f97316" />
            <stop offset="100%" stopColor="#ec4899" />
          </linearGradient>
        </defs>
        <motion.path
          d="M 20% 35% Q 35% 20% 50% 25%"
          stroke="url(#routeGradient)"
          strokeWidth="3"
          fill="none"
          strokeDasharray="6,6"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: animationStep >= 2 ? 1 : 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        />
        <motion.path
          d="M 50% 25% Q 42% 40% 35% 55%"
          stroke="url(#routeGradient)"
          strokeWidth="3"
          fill="none"
          strokeDasharray="6,6"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: animationStep >= 3 ? 1 : 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        />
        <motion.path
          d="M 35% 55% Q 47% 62% 60% 70%"
          stroke="url(#routeGradient)"
          strokeWidth="3"
          fill="none"
          strokeDasharray="6,6"
          initial={{ pathLength: 0 }}
          animate={{ pathLength: animationStep >= 4 ? 1 : 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        />
      </svg>

      {/* Map Controls */}
      <div className="absolute bottom-4 right-4">
        <motion.div 
          className="bg-white rounded-lg shadow-lg p-2 border border-gray-200"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 1, duration: 0.3 }}
        >
          <div className="flex flex-col space-y-1">
            <motion.button 
              className="p-2 hover:bg-gray-100 rounded text-gray-600"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              +
            </motion.button>
            <motion.button 
              className="p-2 hover:bg-gray-100 rounded text-gray-600"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              -
            </motion.button>
          </div>
        </motion.div>
      </div>
    </div>
  );
}

function TimelineDemo({ animationStep }: DemoProps): JSX.Element {
  const activities = [
    { 
      time: '14:00', 
      title: 'Airport Arrival', 
      subtitle: 'Haneda Airport Terminal 2', 
      icon: '✈️', 
      color: 'border-purple-200 bg-purple-50',
      duration: '1h',
      cost: '$0',
      originalCost: '$0',
      savings: 0,
      budgetImpact: 'neutral',
      priceAlert: false
    },
    { 
      time: '16:00', 
      title: 'Hotel Check-in', 
      subtitle: 'Shibuya Sky Hotel', 
      icon: '🏨', 
      color: 'border-green-200 bg-green-50',
      duration: '30m',
      cost: '$120',
      originalCost: '$135',
      savings: 15,
      budgetImpact: 'positive',
      priceAlert: true
    },
    { 
      time: '18:00', 
      title: 'Shibuya Crossing', 
      subtitle: 'World\'s busiest intersection', 
      icon: '🚶', 
      color: 'border-blue-200 bg-blue-50',
      duration: '1h',
      cost: '$0',
      originalCost: '$0',
      savings: 0,
      budgetImpact: 'neutral',
      priceAlert: false
    },
    { 
      time: '20:00', 
      title: 'Izakaya Dinner', 
      subtitle: 'Traditional Japanese pub', 
      icon: '🍜', 
      color: 'border-red-200 bg-red-50',
      duration: '2h',
      cost: '$45',
      originalCost: '$45',
      savings: 0,
      budgetImpact: 'neutral',
      priceAlert: false
    }
  ];

  const totalCost = activities.reduce((sum, activity) => sum + parseInt(activity.cost.replace('$', '')), 0);
  const totalSavings = activities.reduce((sum, activity) => sum + activity.savings, 0);

  return (
    <div className="p-6 space-y-4 h-[450px] bg-gradient-to-br from-gray-50 via-white to-blue-50">
      {/* Enhanced Header with Budget Tracking */}
      <motion.div 
        className="mb-6"
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Day 1 - Tokyo Arrival</h3>
            <p className="text-sm text-gray-500">April 15, 2024 • 4 activities</p>
          </div>
          <div className="flex items-center space-x-2">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: animationStep >= 1 ? 1 : 0 }}
              transition={{ duration: 0.3, type: "spring" }}
            >
              <Badge className="bg-green-500 text-white text-xs">-${totalSavings} saved</Badge>
            </motion.div>
            <Badge variant="outline" className="text-xs">${totalCost} total</Badge>
          </div>
        </div>
        
        {/* Budget Progress Bar */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: animationStep >= 1 ? 1 : 0, y: 0 }}
          transition={{ duration: 0.4 }}
          className="bg-white rounded-lg p-3 border border-gray-200 shadow-sm"
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-gray-600">Daily Budget Progress</span>
            <span className="text-xs font-medium text-gray-900">${totalCost} / $200</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: animationStep >= 2 ? `${(totalCost / 200) * 100}%` : 0 }}
              transition={{ duration: 1, ease: "easeOut" }}
              className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full relative overflow-hidden"
            >
              <motion.div
                animate={{ x: ["-100%", "100%"] }}
                transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
              />
            </motion.div>
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Under budget by $35</span>
            <span>82.5% used</span>
          </div>
        </motion.div>
      </motion.div>
      
      {/* Enhanced Timeline with Budget Features */}
      <div className="space-y-3 overflow-y-auto max-h-72">
        {activities.map((activity, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -30, scale: 0.9 }}
            animate={{ 
              opacity: animationStep >= index + 2 ? 1 : 0.3,
              x: animationStep >= index + 2 ? 0 : -30,
              scale: animationStep >= index + 2 ? 1 : 0.9
            }}
            transition={{ delay: index * 0.15, duration: 0.4, type: "spring" }}
            className={`flex items-start space-x-4 p-4 rounded-xl border-2 ${activity.color} transition-all duration-300 hover:shadow-md cursor-pointer group relative`}
            whileHover={{ scale: 1.02, y: -2 }}
          >
            {/* Price Alert Indicator */}
            {activity.priceAlert && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: animationStep >= index + 2 ? 1 : 0 }}
                transition={{ delay: (index * 0.15) + 0.3, duration: 0.3 }}
                className="absolute -top-2 -right-2 w-5 h-5 bg-orange-500 rounded-full flex items-center justify-center"
              >
                <Bell className="h-3 w-3 text-white" />
              </motion.div>
            )}

            <motion.div 
              className="text-2xl"
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              {activity.icon}
            </motion.div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h4 className="font-semibold text-gray-900 truncate">{activity.title}</h4>
                <div className="flex items-center space-x-2 text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  <span>{activity.time}</span>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-2">{activity.subtitle}</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 text-xs">
                  <span className="text-gray-500">{activity.duration}</span>
                  <span className="text-gray-500">•</span>
                  <div className="flex items-center space-x-1">
                    {activity.savings > 0 ? (
                      <>
                        <span className="line-through text-gray-400">{activity.originalCost}</span>
                        <span className="font-bold text-green-600">{activity.cost}</span>
                        <Badge className="bg-green-500 text-white text-xs">-${activity.savings}</Badge>
                      </>
                    ) : (
                      <span className="font-medium text-gray-700">{activity.cost}</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  {activity.budgetImpact === 'positive' && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: animationStep >= index + 2 ? 1 : 0 }}
                      transition={{ delay: (index * 0.15) + 0.5, duration: 0.3 }}
                    >
                      <TrendingDown className="h-3 w-3 text-green-500" />
                    </motion.div>
                  )}
                  <motion.button 
                    className="p-1 hover:bg-white rounded opacity-0 group-hover:opacity-100 transition-opacity"
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.8 }}
                  >
                    <Heart className="h-3 w-3 text-gray-400" />
                  </motion.button>
                  <motion.button 
                    className="p-1 hover:bg-white rounded opacity-0 group-hover:opacity-100 transition-opacity"
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.8 }}
                  >
                    <Share2 className="h-3 w-3 text-gray-400" />
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Smart Insights */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: animationStep >= 5 ? 1 : 0, y: 0 }}
        transition={{ duration: 0.4 }}
        className="bg-gradient-to-r from-orange-50 to-pink-50 rounded-lg p-3 border border-orange-200"
      >
        <div className="flex items-center space-x-2">
          <Sparkles className="h-4 w-4 text-orange-500" />
          <span className="text-sm font-medium text-orange-800">Smart Tip: Your hotel booking saved $15! Check dinner alternatives to save more.</span>
        </div>
      </motion.div>
    </div>
  );
}

function SharingDemo({ animationStep }: DemoProps): JSX.Element {
  return (
    <div className="p-6 h-[450px] flex flex-col bg-gradient-to-br from-gray-50 to-white">
      {/* Trip Header */}
      <motion.div 
        className="text-center space-y-4 mb-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <motion.div 
          className="w-16 h-16 bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl mx-auto flex items-center justify-center"
          animate={{ rotate: [0, 5, -5, 0] }}
          transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
        >
          <MapPin className="h-8 w-8 text-white" />
        </motion.div>
        <div>
          <h3 className="text-xl font-semibold text-gray-900">Tokyo Adventure</h3>
          <p className="text-gray-600">7 days • 12 activities • $1,200 budget</p>
        </div>
        <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
          <motion.div 
            className="flex items-center space-x-1"
            whileHover={{ scale: 1.05 }}
          >
            <Eye className="h-4 w-4" />
            <span>127 views</span>
          </motion.div>
          <motion.div 
            className="flex items-center space-x-1"
            whileHover={{ scale: 1.05 }}
          >
            <Heart className="h-4 w-4" />
            <span>23 likes</span>
          </motion.div>
          <motion.div 
            className="flex items-center space-x-1"
            whileHover={{ scale: 1.05 }}
          >
            <MessageCircle className="h-4 w-4" />
            <span>8 comments</span>
          </motion.div>
        </div>
      </motion.div>
      
      {/* Share Actions */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ 
          scale: animationStep >= 1 ? 1 : 0,
          opacity: animationStep >= 1 ? 1 : 0
        }}
        transition={{ duration: 0.4, type: "spring" }}
        className="flex justify-center space-x-3 mb-6"
      >
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
        </motion.div>
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button variant="outline" className="px-4 py-2 rounded-lg">
            <Copy className="h-4 w-4 mr-2" />
            Copy Link
          </Button>
        </motion.div>
        <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
          <Button variant="outline" className="px-4 py-2 rounded-lg">
            <ExternalLink className="h-4 w-4 mr-2" />
            View Public
          </Button>
        </motion.div>
      </motion.div>

      {/* Activity Feed */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ 
          opacity: animationStep >= 2 ? 1 : 0,
          y: animationStep >= 2 ? 0 : 20
        }}
        transition={{ duration: 0.4 }}
        className="flex-1 space-y-3 overflow-y-auto"
      >
        <div className="text-sm font-medium text-gray-700 mb-3">Recent Activity</div>
        <div className="space-y-3">
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2, duration: 0.3 }}
            className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg border border-green-200"
            whileHover={{ scale: 1.02, x: 5 }}
          >
            <motion.div 
              className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <Users className="h-4 w-4 text-white" />
            </motion.div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Sarah joined your trip</p>
              <p className="text-xs text-gray-600">2 minutes ago</p>
            </div>
          </motion.div>
          
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.3 }}
            className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200"
            whileHover={{ scale: 1.02, x: 5 }}
          >
            <motion.div 
              className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
            >
              <Heart className="h-4 w-4 text-white" />
            </motion.div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Mike liked your itinerary</p>
              <p className="text-xs text-gray-600">5 minutes ago</p>
            </div>
          </motion.div>
          
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.6, duration: 0.3 }}
            className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg border border-purple-200"
            whileHover={{ scale: 1.02, x: 5 }}
          >
            <motion.div 
              className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center"
              animate={{ scale: [1, 1.1, 1] }}
              transition={{ duration: 2, repeat: Infinity, delay: 1 }}
            >
              <MessageCircle className="h-4 w-4 text-white" />
            </motion.div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">Emma added a comment</p>
              <p className="text-xs text-gray-600">10 minutes ago</p>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}

function ExportDemo({ animationStep }: DemoProps): JSX.Element {
  const exportOptions = [
    { 
      name: 'PDF Export', 
      description: 'Clean, printable format', 
      icon: Download, 
      color: 'bg-red-500',
      badge: 'Premium'
    },
    { 
      name: 'Google Maps', 
      description: 'Navigate with ease', 
      icon: MapPin, 
      color: 'bg-green-500',
      badge: 'Free'
    },
    { 
      name: 'Calendar Sync', 
      description: 'Add to your schedule', 
      icon: Calendar, 
      color: 'bg-blue-500',
      badge: 'Free'
    }
  ];

  return (
    <div className="p-4 sm:p-6 h-[350px] sm:h-[400px] lg:h-[450px] flex flex-col justify-center bg-gradient-to-br from-gray-50 to-white">
      <div className="space-y-5 sm:space-y-6">
        {/* Header */}
        <motion.div 
          className="text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <motion.div 
            className="w-12 h-12 sm:w-12 sm:h-12 bg-gradient-to-r from-orange-500 to-pink-500 rounded-xl mx-auto mb-4 sm:mb-4 flex items-center justify-center"
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
          >
            <Download className="h-6 w-6 sm:h-6 sm:w-6 text-white" />
          </motion.div>
          <h3 className="text-lg sm:text-lg font-semibold text-gray-900 mb-2 sm:mb-2">Export Your Trip</h3>
          <p className="text-gray-700 text-sm sm:text-sm font-medium">Choose your preferred format</p>
        </motion.div>
        
        {/* Export Options */}
        <div className="space-y-3 sm:space-y-3">
          {exportOptions.map((option, index) => (
            <motion.button
              key={index}
              initial={{ opacity: 0, scale: 0.9, x: -20 }}
              animate={{ 
                opacity: animationStep >= index + 1 ? 1 : 0.4,
                scale: animationStep >= index + 1 ? 1 : 0.9,
                x: animationStep >= index + 1 ? 0 : -20
              }}
              transition={{ delay: index * 0.2, duration: 0.4, type: "spring" }}
              className="w-full flex items-center space-x-4 sm:space-x-4 p-4 sm:p-4 bg-white border-2 border-gray-300 rounded-xl sm:rounded-xl hover:border-orange-300 hover:bg-orange-50 transition-all duration-200 group touch-spacing shadow-md"
              whileHover={{ scale: 1.02, x: 5 }}
              whileTap={{ scale: 0.98 }}
            >
              <motion.div 
                className={`w-12 h-12 sm:w-12 sm:h-12 ${option.color} rounded-xl sm:rounded-xl flex items-center justify-center shadow-md`}
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
              >
                <option.icon className="h-6 w-6 sm:h-6 sm:w-6 text-white" />
              </motion.div>
              <div className="flex-1 text-left min-w-0">
                <div className="flex items-center space-x-2">
                  <h4 className="font-semibold text-gray-900 text-base sm:text-base truncate">{option.name}</h4>
                  <Badge 
                    variant={option.badge === 'Premium' ? 'default' : 'outline'} 
                    className={`text-xs flex-shrink-0 font-medium ${option.badge === 'Premium' ? 'bg-orange-500' : ''}`}
                  >
                    {option.badge}
                  </Badge>
                </div>
                <p className="text-sm sm:text-sm text-gray-600 truncate font-medium">{option.description}</p>
              </div>
              <motion.div
                animate={{ y: [0, -3, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="flex-shrink-0"
              >
                <ArrowDown className="h-5 w-5 text-gray-400 group-hover:text-orange-500 transition-colors" />
              </motion.div>
            </motion.button>
          ))}
        </div>

        {/* Export Stats */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ 
            opacity: animationStep >= 4 ? 1 : 0,
            y: animationStep >= 4 ? 0 : 10
          }}
          transition={{ duration: 0.4 }}
          className="bg-gray-100 rounded-lg p-4 sm:p-4 border-2 border-gray-200"
        >
          <div className="grid grid-cols-3 gap-3 sm:gap-4 text-center">
            <motion.div
              whileHover={{ scale: 1.05 }}
            >
              <motion.div 
                className="text-lg sm:text-lg font-bold text-gray-900"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                1.2k
              </motion.div>
              <div className="text-xs text-gray-700 font-medium">Downloads</div>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
            >
              <motion.div 
                className="text-lg sm:text-lg font-bold text-gray-900"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
              >
                4.9
              </motion.div>
              <div className="text-xs text-gray-700 font-medium">Rating</div>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
            >
              <motion.div 
                className="text-lg sm:text-lg font-bold text-gray-900"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity, delay: 1 }}
              >
                24/7
              </motion.div>
              <div className="text-xs text-gray-700 font-medium">Support</div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}