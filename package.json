{"name": "travelviz", "version": "1.0.0", "private": true, "workspaces": ["packages/*"], "scripts": {"dev": "concurrently \"pnpm --filter @travelviz/hub dev\" \"pnpm --filter @travelviz/web dev\"", "build": "pnpm --filter @travelviz/shared build && pnpm --filter @travelviz/hub build && pnpm --filter @travelviz/web build", "start": "concurrently \"pnpm --filter @travelviz/hub start\" \"pnpm --filter @travelviz/web start\"", "lint": "pnpm --filter \"./packages/*\" lint", "type-check": "pnpm --filter \"./packages/*\" type-check", "clean": "pnpm --filter \"./packages/*\" clean || true", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "concurrently": "^8.2.2", "eslint": "^8.57.0", "husky": "^9.1.7", "prettier": "^3.3.3", "typescript": "^5.4.0"}, "packageManager": "pnpm@9.0.0"}