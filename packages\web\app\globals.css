@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Cal+Sans:wght@600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
  
  /* TravelViz Color System */
  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-300: #fdba74;
  --orange-400: #fb923c;
  --orange-500: #ff6b35;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-800: #9a3412;
  --orange-900: #7c2d12;
  
  --navy-50: #f0f4f8;
  --navy-100: #d9e2ec;
  --navy-200: #bcccdc;
  --navy-300: #9fb3c8;
  --navy-400: #829ab1;
  --navy-500: #627d98;
  --navy-600: #486581;
  --navy-700: #334e68;
  --navy-800: #243b53;
  --navy-900: #1e3a5f;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 21 90% 60%;
    --primary-foreground: 0 0% 98%;
    --secondary: 210 33% 25%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 21 90% 60%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 21 90% 60%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 21 90% 60%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Inter', sans-serif;
    /* Prevent zoom on iOS when focusing inputs */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Cal Sans', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-orange-500 hover:bg-orange-600 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:shadow-lg hover:scale-105;
    /* Ensure minimum touch target size */
    min-height: 44px;
    min-width: 44px;
  }
  
  .btn-secondary {
    @apply border-2 border-gray-300 hover:border-gray-400 text-gray-700 font-medium px-6 py-3 rounded-xl transition-all duration-200;
    min-height: 44px;
    min-width: 44px;
  }
  
  .btn-ghost {
    @apply text-gray-600 hover:text-gray-900 font-medium px-6 py-3 rounded-xl transition-all duration-200 hover:bg-gray-50;
    min-height: 44px;
    min-width: 44px;
  }
  
  .card-hover {
    @apply transition-all duration-200 hover:shadow-xl hover:-translate-y-1;
  }
  
  .text-gradient {
    @apply bg-gradient-to-r from-orange-500 to-pink-500 bg-clip-text text-transparent;
  }
  
  .hero-gradient {
    background: linear-gradient(135deg, #ff6b35 0%, #f093fb 100%);
  }

  /* Mobile-first responsive text sizing */
  .responsive-text-hero {
    @apply text-3xl leading-tight;
  }
  
  @screen sm {
    .responsive-text-hero {
      @apply text-4xl;
    }
  }
  
  @screen md {
    .responsive-text-hero {
      @apply text-5xl;
    }
  }
  
  @screen lg {
    .responsive-text-hero {
      @apply text-6xl;
    }
  }
  
  @screen xl {
    .responsive-text-hero {
      @apply text-7xl;
    }
  }

  .responsive-text-subtitle {
    @apply text-lg leading-relaxed;
  }
  
  @screen sm {
    .responsive-text-subtitle {
      @apply text-xl;
    }
  }
  
  @screen md {
    .responsive-text-subtitle {
      @apply text-2xl;
    }
  }
}

.marker {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg cursor-pointer transition-transform hover:scale-110;
  /* Ensure touch targets are large enough */
  min-width: 44px;
  min-height: 44px;
}

.marker-activity { @apply bg-blue-500; }
.marker-restaurant { @apply bg-red-500; }
.marker-accommodation { @apply bg-green-500; }
.marker-transport { @apply bg-purple-500; }

/* Loading animations */
@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7); }
  50% { box-shadow: 0 0 0 10px rgba(255, 107, 53, 0); }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Smooth transitions for route changes */
.page-transition {
  @apply transition-all duration-200 ease-out;
}

/* Mobile-specific improvements */
@media (max-width: 768px) {
  /* Improve touch targets */
  .btn-primary,
  .btn-secondary,
  .btn-ghost,
  button,
  .clickable {
    @apply min-h-[44px] min-w-[44px];
    /* Add padding for better touch experience */
    padding: 12px 16px;
  }
  
  /* Better mobile spacing */
  .mobile-spacing {
    @apply px-4 py-6;
  }
  
  /* Mobile-friendly cards */
  .mobile-card {
    @apply p-4 rounded-lg shadow-sm;
    /* Ensure adequate spacing for mobile */
    margin-bottom: 16px;
  }
  
  /* Improved mobile navigation */
  .mobile-nav {
    @apply text-sm py-3 px-4;
    /* Adequate touch target size */
    min-height: 48px;
  }
  
  /* Mobile form improvements */
  .mobile-form input,
  .mobile-form textarea,
  .mobile-form select {
    @apply p-3 text-base rounded-lg border-2;
    /* Prevent zoom on iOS */
    font-size: 16px;
    min-height: 48px;
  }
  
  /* Mobile-friendly modals */
  .mobile-modal {
    @apply p-4 max-h-screen overflow-y-auto;
    /* Safe area handling */
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* Better mobile typography */
  .mobile-text {
    @apply text-sm leading-relaxed;
    /* Better readability on small screens */
  }
  
  /* Mobile-optimized grids */
  .mobile-grid {
    @apply grid grid-cols-1 gap-4;
    /* Single column on mobile */
  }
  
  /* Mobile timeline improvements */
  .mobile-timeline {
    @apply space-y-4 relative pl-6;
  }
  
  .mobile-timeline-item {
    @apply relative pb-4;
    /* Better spacing between timeline items */
  }
  
  /* Mobile map container */
  .mobile-map {
    @apply h-64 w-full rounded-lg;
    /* Adequate map size for mobile */
  }
  
  /* Mobile-friendly tables */
  .mobile-table {
    @apply text-xs overflow-x-auto;
    /* Handle table overflow */
    min-width: 100%;
  }
  
  /* Mobile search improvements */
  .mobile-search {
    @apply sticky top-0 z-10 bg-white border-b p-3;
    /* Sticky search on mobile */
  }
  
  /* Mobile dropdown improvements */
  .mobile-dropdown {
    @apply absolute top-full left-0 right-0 bg-white border rounded-lg shadow-lg mt-1 z-20;
    /* Full width dropdown */
  }
  
  /* Mobile-friendly alerts */
  .mobile-alert {
    @apply p-3 rounded-lg mb-4 text-sm;
    /* Compact alerts */
  }
  
  /* Mobile sidebar improvements */
  .mobile-sidebar {
    @apply fixed inset-y-0 left-0 w-64 bg-white transform transition-transform duration-300 ease-in-out z-30;
    /* Slide-out sidebar */
  }
  
  .mobile-sidebar.closed {
    @apply -translate-x-full;
  }
  
  /* Mobile overlay */
  .mobile-overlay {
    @apply fixed inset-0 bg-black bg-opacity-50 z-20;
    /* Overlay for modals/sidebar */
  }

  /* Mobile hero text optimization */
  .mobile-hero-text {
    @apply text-2xl leading-tight font-bold;
    /* Smaller hero text for mobile */
  }

  /* Mobile button groups */
  .mobile-button-group {
    @apply flex flex-col space-y-3;
    /* Stack buttons vertically */
  }

  .mobile-button-group button {
    @apply w-full min-h-[48px] touch-spacing;
    /* Full width buttons with adequate touch target */
  }

  /* Mobile card stacking */
  .mobile-card-stack {
    @apply space-y-4;
    /* Vertical card stacking */
  }

  /* Mobile input groups */
  .mobile-input-group {
    @apply space-y-3;
    /* Form input spacing */
  }

  /* Mobile navigation improvements */
  .mobile-nav-item {
    @apply block py-3 px-4 text-base;
    /* Mobile navigation items */
    min-height: 48px;
  }

  /* Mobile footer improvements */
  .mobile-footer {
    @apply py-8 px-4;
    /* Footer adjustments */
  }

  .mobile-footer-section {
    @apply mb-6;
    /* Footer section spacing */
  }

  /* Mobile video/showcase improvements */
  .mobile-showcase {
    @apply px-4 py-6;
    /* Showcase section mobile adjustments */
  }

  /* Mobile form validation */
  .mobile-form-error {
    @apply text-xs text-red-600 mt-1;
    /* Form error styling */
  }

  /* Mobile loading states */
  .mobile-loading {
    @apply flex items-center justify-center py-8;
    /* Loading state */
  }

  /* Mobile empty states */
  .mobile-empty-state {
    @apply text-center py-12 px-4;
    /* Empty state styling */
  }

  /* Mobile pagination */
  .mobile-pagination {
    @apply flex justify-center space-x-1 py-4;
    /* Pagination adjustments */
  }

  /* Mobile tabs */
  .mobile-tabs {
    @apply flex overflow-x-auto space-x-1 p-1 bg-gray-100 rounded-lg;
    /* Scrollable tabs */
  }

  .mobile-tab {
    @apply flex-shrink-0 px-4 py-2 rounded-md text-sm font-medium transition-colors min-h-[44px] flex items-center;
    /* Tab styling with adequate touch target */
  }

  /* Additional mobile improvements */
  .touch-spacing {
    /* Ensure minimum touch target size */
    min-width: 44px;
    min-height: 44px;
  }

  .mobile-safe-spacing {
    /* Safe area adjustments for mobile devices */
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Tablet-specific improvements */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-grid {
    @apply grid-cols-2 gap-6;
  }
  
  .tablet-spacing {
    @apply px-6 py-8;
  }
  
  .tablet-text {
    @apply text-lg;
  }

  .tablet-hero-text {
    @apply text-4xl leading-tight;
  }

  .tablet-button-group {
    @apply flex flex-row space-x-4 space-y-0;
  }
}

/* Desktop improvements */
@media (min-width: 1024px) {
  .desktop-grid {
    @apply grid-cols-3 gap-8;
  }
  
  .desktop-spacing {
    @apply px-8 py-12;
  }
  
  /* Better hover states on desktop */
  .desktop-hover:hover {
    @apply transform -translate-y-1 shadow-xl;
  }

  .desktop-hero-text {
    @apply text-5xl lg:text-6xl xl:text-7xl leading-tight;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover-effect:hover {
    transform: none;
    box-shadow: none;
  }

  /* Larger touch targets */
  button, .clickable {
    @apply min-h-[48px] min-w-[48px];
  }

  /* Better spacing for touch */
  .touch-spacing {
    @apply p-4;
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Crisp borders and shadows */
  .crisp-border {
    border-width: 0.5px;
  }
}

/* Print styles */
@media print {
  .no-print {
    @apply hidden;
  }
  
  .print-friendly {
    @apply text-black bg-white shadow-none;
  }
  
  /* Ensure good contrast for printing */
  .print-text {
    @apply text-gray-900;
  }
  
  /* Remove unnecessary elements for print */
  .print-hide {
    @apply hidden;
  }
  
  /* Optimize page breaks */
  .print-page-break {
    page-break-before: always;
  }
  
  .print-no-break {
    page-break-inside: avoid;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .high-contrast {
    @apply border-2 border-black;
  }
  
  .high-contrast-text {
    @apply text-black;
  }
  
  .high-contrast-bg {
    @apply bg-white;
  }
}

/* Focus improvements for keyboard navigation */
.focus-visible:focus-visible {
  @apply outline-2 outline-offset-2 outline-orange-500;
}

/* Skip link for screen readers */
.skip-link {
  @apply absolute -top-10 left-4 bg-orange-500 text-white px-4 py-2 rounded-lg z-50 focus:top-4;
}

/* Screen reader only content */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

/* Mobile-specific viewport fixes */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari specific fixes */
  .ios-fix {
    -webkit-appearance: none;
    -webkit-border-radius: 0;
  }
}

/* Android specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .android-fix {
    -webkit-appearance: none;
  }
}

/* Landscape mobile optimizations */
@media screen and (max-height: 500px) and (orientation: landscape) {
  .landscape-mobile {
    @apply py-2;
  }
  
  .landscape-mobile-hero {
    @apply py-8;
  }
  
  .landscape-mobile-text {
    @apply text-xl;
  }
}

/* Very small screens (older phones) */
@media screen and (max-width: 320px) {
  .tiny-screen {
    @apply text-sm px-2;
  }
  
  .tiny-screen-hero {
    @apply text-xl px-3;
  }
  
  .tiny-screen-button {
    @apply text-sm px-3 py-2;
  }
}

/* Large screens optimization */
@media screen and (min-width: 1920px) {
  .large-screen {
    @apply max-w-7xl mx-auto;
  }
  
  .large-screen-text {
    @apply text-8xl;
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  .safe-area-inset-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
  
  .safe-area-inset-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-area-inset-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}