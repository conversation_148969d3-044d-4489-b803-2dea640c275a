export declare const API_ENDPOINTS: {
    readonly USERS: "/api/users";
    readonly TRIPS: "/api/trips";
    readonly AUTH: "/api/auth";
    readonly UPLOAD: "/api/upload";
};
export declare const HTTP_STATUS: {
    readonly OK: 200;
    readonly CREATED: 201;
    readonly BAD_REQUEST: 400;
    readonly UNAUTHORIZED: 401;
    readonly FORBIDDEN: 403;
    readonly NOT_FOUND: 404;
    readonly INTERNAL_SERVER_ERROR: 500;
};
export declare const ERROR_CODES: {
    readonly VALIDATION_ERROR: "VALIDATION_ERROR";
    readonly UNAUTHORIZED: "UNAUTHORIZED";
    readonly FORBIDDEN: "FORBIDDEN";
    readonly NOT_FOUND: "NOT_FOUND";
    readonly INTERNAL_SERVER_ERROR: "INTERNAL_SERVER_ERROR";
};
export declare const DEFAULT_PAGINATION: {
    readonly page: 1;
    readonly limit: 10;
    readonly maxLimit: 100;
};
export declare const DATE_FORMATS: {
    readonly ISO: "YYYY-MM-DDTHH:mm:ssZ";
    readonly SHORT: "YYYY-MM-DD";
    readonly LONG: "MMMM DD, YYYY";
};
export declare const APP_CONFIG: {
    readonly NAME: "TravelViz";
    readonly VERSION: "1.0.0";
    readonly DESCRIPTION: "AI-powered travel planning platform";
};
