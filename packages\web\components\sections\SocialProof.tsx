"use client";

import { motion } from 'framer-motion';
import { Users, Heart, Globe, Compass, MapPin, Star, DollarSign, Clock, TrendingUp, Bell } from 'lucide-react';

const communityValues = [
  {
    title: "Genuine Passion",
    description: "Built by travelers who understand the real challenges of exploration and discovery",
    icon: Heart,
    color: "from-red-500 to-pink-500"
  },
  {
    title: "Community Driven",
    description: "Every feature shaped by our early community of authentic travelers and explorers",
    icon: Users,
    color: "from-blue-500 to-indigo-500"
  },
  {
    title: "No Fake Promises",
    description: "Transparent development, honest communication, and real solutions to travel problems",
    icon: Star,
    color: "from-yellow-500 to-orange-500"
  }
];

const keyFeatures = [
  {
    title: "Smart Money Features",
    description: "Tools designed to help you save money and track expenses transparently",
    icon: DollarSign,
    color: "from-green-500 to-emerald-500",
    features: [
      "Automatic price drop alerts for flights & hotels",
      "Budget tracking within your itinerary", 
      "Find deals on AI-suggested places",
      "Share costs transparently with travel partners"
    ]
  },
  {
    title: "Time-Saving AI",
    description: "Transform AI conversations into visual itineraries in seconds, not hours",
    icon: Clock,
    color: "from-blue-500 to-indigo-500",
    features: [
      "Seamless AI conversation → bookable itinerary",
      "Easy sharing with your travel group",
      "Price tracking without manual checking",
      "Full offline access during trips"
    ]
  },
  {
    title: "Group Coordination",
    description: "Seamless collaboration with real-time editing and approval tracking",
    icon: Users,
    color: "from-purple-500 to-pink-500",
    features: [
      "One link to share entire trip plan",
      "See who has viewed and approved plans",
      "Real-time collaborative editing capabilities",
      "Group booking coordination made easy"
    ]
  }
];

export function SocialProof() {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mission Statement */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Built by Travelers, for Travelers
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="text-xl text-gray-600 leading-relaxed mb-8">
              Our app is built from the ground up with one simple mission: to make travel planning and experiences more accessible, enjoyable, and authentic for everyone.
            </p>
            <p className="text-lg text-gray-600 leading-relaxed">
              We're just starting our journey, but we're committed to growing alongside our community of travelers. 
              No fake promises, no artificial hype - just a sincere platform created by travelers, for travelers. 
              Join us in building something meaningful together.
            </p>
          </div>
        </motion.div>

        {/* Community Values */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {communityValues.map((value, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.2, duration: 0.6 }}
              className="text-center"
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${value.color} mb-6`}>
                <value.icon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
              <p className="text-gray-600 leading-relaxed">{value.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Key Features That Save Money & Time */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.6, duration: 0.6 }}
          className="mb-16"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Features That Actually Save You Money & Time
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Every feature is designed with one goal: make travel planning more efficient and cost-effective
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {keyFeatures.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
                className="bg-gray-50 rounded-2xl p-8"
              >
                <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r ${feature.color} mb-6`}>
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-4">{feature.title}</h4>
                <p className="text-gray-600 leading-relaxed mb-6">{feature.description}</p>
                
                <ul className="space-y-3">
                  {feature.features.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-700 text-sm">{item}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Founder's Message */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="bg-gradient-to-r from-orange-50 to-pink-50 rounded-2xl p-8 md:p-12 text-center border border-orange-100 mb-16"
        >
          <div className="max-w-3xl mx-auto">
            <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6">
              LZ
            </div>
            <blockquote className="text-xl md:text-2xl text-gray-700 leading-relaxed mb-6">
              "As fellow travelers, we understand the challenges of discovering new places and connecting with like-minded adventurers. 
              Our app is built from the ground up with one simple mission: to make travel planning and experiences more accessible, 
              enjoyable, and authentic for everyone."
            </blockquote>
            <div className="text-gray-600">
              <div className="font-semibold">Louis Zeng</div>
              <div className="text-sm">Founder & Lead Developer</div>
              
            </div>
          </div>
        </motion.div>

        {/* Community Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 1, duration: 0.6 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">Join TravelViz to Be Part of Our Early Travel Community</h3>
            <p className="text-xl mb-6 text-white/90">
              Help shape the future of travel experiences. We're building this together with our community of authentic travelers.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <button className="bg-white text-orange-500 hover:bg-gray-100 px-8 py-3 rounded-xl font-semibold transition-all duration-200 hover:scale-105 flex items-center">
                <Heart className="h-5 w-5 mr-2" />
                Join Our Community
              </button>
              <span className="text-white/80 text-sm">No fake promises • Just authentic travel experiences</span>
            </div>
          </div>
        </motion.div>

        {/* Trust Indicators */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 1.2, duration: 0.6 }}
          className="flex flex-col md:flex-row items-center justify-center space-y-4 md:space-y-0 md:space-x-12 mt-16 pt-12 border-t border-gray-200"
        >
          <div className="flex items-center space-x-2 text-gray-600">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span className="font-medium">Built by Travelers, for Travelers</span>
          </div>
          <div className="flex items-center space-x-2 text-gray-600">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span className="font-medium">Community-Driven Development</span>
          </div>
          <div className="flex items-center space-x-2 text-gray-600">
            <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
            <span className="font-medium">Authentic & Transparent</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
}