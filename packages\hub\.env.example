# Server Configuration
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Database & Auth (Supabase)
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
DATABASE_URL=postgresql://postgres:password@localhost:5432/travelviz

# AI Services (OpenRouter for Claude/GPT-4/Gemini)
OPENROUTER_API_KEY=your_openrouter_api_key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1

# Flight APIs
# Duffel API (US Domestic Flights)
DUFFEL_API_KEY=your_duffel_api_key
DUFFEL_BASE_URL=https://api.duffel.com

# Travelpayouts (International Flights)
TRAVELPAYOUTS_API_KEY=your_travelpayouts_api_key
TRAVELPAYOUTS_BASE_URL=https://api.travelpayouts.com

# Hotel APIs
# Hotels.com via Expedia
EXPEDIA_API_KEY=your_expedia_api_key
EXPEDIA_SECRET=your_expedia_secret

# Agoda API
AGODA_API_KEY=your_agoda_api_key

# Booking.com API (when available)
BOOKING_API_KEY=your_booking_api_key

# Maps & Location
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token
MAPBOX_SECRET_TOKEN=your_mapbox_secret_token

# Caching & Queue (Redis/Upstash)
REDIS_URL=your_upstash_redis_url
REDIS_TOKEN=your_upstash_redis_token

# Email Service (Resend)
RESEND_API_KEY=your_resend_api_key
FROM_EMAIL=<EMAIL>

# Security
JWT_SECRET=your_jwt_secret_key_minimum_32_characters
ENCRYPTION_KEY=your_encryption_key_32_characters

# Monitoring & Analytics
SENTRY_DSN=your_sentry_dsn

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Background Jobs
BULL_REDIS_URL=your_redis_url_for_bull_queue

# Affiliate Tracking
AFFILIATE_SECRET=your_affiliate_tracking_secret

# Development
DEBUG=travelviz:*
LOG_LEVEL=debug
