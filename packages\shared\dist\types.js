"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TripSchema = exports.UserSchema = void 0;
const zod_1 = require("zod");
// User types
exports.UserSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    email: zod_1.z.string().email(),
    name: zod_1.z.string(),
    avatar: zod_1.z.string().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
});
// Trip types
exports.TripSchema = zod_1.z.object({
    id: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid(),
    title: zod_1.z.string(),
    description: zod_1.z.string().optional(),
    startDate: zod_1.z.date(),
    endDate: zod_1.z.date(),
    destinations: zod_1.z.array(zod_1.z.string()),
    isPublic: zod_1.z.boolean().default(false),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date(),
});
