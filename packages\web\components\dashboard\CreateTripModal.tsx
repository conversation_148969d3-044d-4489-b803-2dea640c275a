"use client";

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, MapPin, Sparkles, FileText, Bot } from 'lucide-react';
import { format } from 'date-fns';

interface CreateTripModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTrip: (tripData: any) => void;
}

const quickDestinations = [
  '🗾 Japan', '🇮🇹 Italy', '🇫🇷 France', '🇪🇸 Spain', 
  '🇬🇧 UK', '🇺🇸 USA', '🇹🇭 Thailand', '🇦🇺 Australia'
];

export function CreateTripModal({ isOpen, onClose, onCreateTrip }: CreateTripModalProps) {
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    title: '',
    destination: '',
    description: '',
    startDate: null as Date | null,
    endDate: null as Date | null,
    method: 'scratch' // 'scratch', 'ai', 'template', 'import'
  });

  const methods = [
    {
      id: 'scratch',
      title: 'Start from Scratch',
      description: 'Create a blank itinerary and add activities manually',
      icon: MapPin,
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'ai',
      title: 'AI Assistant',
      description: 'Let AI create a personalized itinerary for you',
      icon: Bot,
      color: 'from-purple-500 to-purple-600'
    },
    {
      id: 'template',
      title: 'Use Template',
      description: 'Start with a proven itinerary template',
      icon: FileText,
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'import',
      title: 'Import Document',
      description: 'Upload a PDF or paste text from ChatGPT',
      icon: Sparkles,
      color: 'from-orange-500 to-orange-600'
    }
  ];

  const handleNext = () => {
    if (step < 3) setStep(step + 1);
  };

  const handleBack = () => {
    if (step > 1) setStep(step - 1);
  };

  const handleSubmit = () => {
    onCreateTrip(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            Create New Trip
          </DialogTitle>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center space-x-2 mb-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                i <= step 
                  ? 'bg-orange-500 text-white' 
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {i}
              </div>
              {i < 3 && (
                <div className={`w-12 h-1 mx-2 ${
                  i < step ? 'bg-orange-500' : 'bg-gray-200'
                }`}></div>
              )}
            </div>
          ))}
        </div>

        {/* Step 1: Method Selection */}
        {step === 1 && (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                How would you like to create your trip?
              </h3>
              <p className="text-gray-600">Choose the method that works best for you</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {methods.map((method) => (
                <button
                  key={method.id}
                  onClick={() => setFormData({ ...formData, method: method.id })}
                  className={`p-6 rounded-xl border-2 text-left transition-all duration-200 hover:scale-105 ${
                    formData.method === method.id
                      ? 'border-orange-500 bg-orange-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className={`inline-flex p-3 rounded-lg bg-gradient-to-r ${method.color} mb-4`}>
                    <method.icon className="h-6 w-6 text-white" />
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">{method.title}</h4>
                  <p className="text-sm text-gray-600">{method.description}</p>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Step 2: Basic Information */}
        {step === 2 && (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Tell us about your trip
              </h3>
              <p className="text-gray-600">We'll use this to create your perfect itinerary</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Trip Title</Label>
                <Input
                  id="title"
                  placeholder="e.g., 7 Days in Tokyo & Kyoto"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                />
              </div>

              <div>
                <Label htmlFor="destination">Destination</Label>
                <Input
                  id="destination"
                  placeholder="e.g., Japan"
                  value={formData.destination}
                  onChange={(e) => setFormData({ ...formData, destination: e.target.value })}
                />
                <div className="flex flex-wrap gap-2 mt-2">
                  {quickDestinations.map((dest) => (
                    <Badge
                      key={dest}
                      variant="outline"
                      className="cursor-pointer hover:bg-gray-100"
                      onClick={() => setFormData({ ...formData, destination: dest.split(' ')[1] })}
                    >
                      {dest}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Start Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.startDate ? format(formData.startDate, 'PPP') : 'Pick a date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.startDate || undefined}
                        onSelect={(date) => setFormData({ ...formData, startDate: date || null })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>

                <div>
                  <Label>End Date</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" className="w-full justify-start text-left font-normal">
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.endDate ? format(formData.endDate, 'PPP') : 'Pick a date'}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.endDate || undefined}
                        onSelect={(date) => setFormData({ ...formData, endDate: date || null })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description (Optional)</Label>
                <Textarea
                  id="description"
                  placeholder="Tell us about your travel style, interests, or any specific requirements..."
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>
            </div>
          </div>
        )}

        {/* Step 3: Confirmation */}
        {step === 3 && (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Ready to create your trip?
              </h3>
              <p className="text-gray-600">Review your details and let's get started!</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-6 space-y-4">
              <div>
                <h4 className="font-semibold text-gray-900">{formData.title}</h4>
                <p className="text-gray-600">{formData.destination}</p>
              </div>
              
              {formData.startDate && formData.endDate && (
                <div className="flex items-center text-sm text-gray-600">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {format(formData.startDate, 'MMM d')} - {format(formData.endDate, 'MMM d, yyyy')}
                </div>
              )}

              <div className="flex items-center text-sm">
                <Badge variant="outline">
                  {methods.find(m => m.id === formData.method)?.title}
                </Badge>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6">
          <Button
            variant="outline"
            onClick={step === 1 ? onClose : handleBack}
            disabled={step === 1}
          >
            {step === 1 ? 'Cancel' : 'Back'}
          </Button>
          
          <Button
            onClick={step === 3 ? handleSubmit : handleNext}
            className="btn-primary"
          >
            {step === 3 ? 'Create Trip' : 'Next'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}