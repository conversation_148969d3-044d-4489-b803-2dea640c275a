"use client";

import { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
  MapPin, 
  Calendar, 
  Clock, 
  DollarSign, 
  Share2, 
  Download, 
  Settings, 
  Plus, 
  MoreHorizontal,
  Edit,
  Trash2,
  GripVertical,
  Users,
  Eye,
  Save,
  Menu,
  X
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

// Mock data for the trip
const mockTrip = {
  id: '1',
  title: '7 Days in Tokyo & Kyoto',
  description: 'A perfect blend of modern Tokyo and traditional Kyoto',
  destination: 'Japan',
  startDate: '2024-04-15',
  endDate: '2024-04-22',
  status: 'upcoming',
  collaborators: [
    { id: 1, name: '<PERSON>', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=40' },
    { id: 2, name: 'Mike Johnson', avatar: 'https://images.pexels.com/photos/1040880/pexels-photo-1040880.jpeg?auto=compress&cs=tinysrgb&w=40' }
  ],
  days: [
    {
      day: 1,
      date: '2024-04-15',
      theme: 'Tokyo Arrival & Shibuya',
      items: [
        {
          id: 1,
          time: '14:00',
          title: 'Arrive at Haneda Airport',
          description: 'International flight arrival, collect luggage and get JR Pass',
          location: { name: 'Haneda Airport', address: 'Tokyo, Japan', lat: 35.5494, lng: 139.7798 },
          category: 'transport',
          duration: 60,
          cost: 0
        },
        {
          id: 2,
          time: '16:00',
          title: 'Check into Hotel in Shibuya',
          description: 'Modern hotel in the heart of Shibuya district',
          location: { name: 'Shibuya District', address: 'Shibuya, Tokyo, Japan', lat: 35.6598, lng: 139.7006 },
          category: 'accommodation',
          duration: 30,
          cost: 120
        },
        {
          id: 3,
          time: '18:00',
          title: 'Shibuya Crossing Experience',
          description: 'Experience the world\'s busiest pedestrian crossing',
          location: { name: 'Shibuya Crossing', address: 'Shibuya, Tokyo, Japan', lat: 35.6598, lng: 139.7006 },
          category: 'activity',
          duration: 90,
          cost: 0
        },
        {
          id: 4,
          time: '20:00',
          title: 'Dinner at Izakaya',
          description: 'Traditional Japanese pub experience',
          location: { name: 'Local Izakaya', address: 'Shibuya, Tokyo, Japan', lat: 35.6598, lng: 139.7006 },
          category: 'restaurant',
          duration: 120,
          cost: 45
        }
      ]
    },
    {
      day: 2,
      date: '2024-04-16',
      theme: 'Traditional Tokyo',
      items: [
        {
          id: 5,
          time: '09:00',
          title: 'Senso-ji Temple',
          description: 'Tokyo\'s oldest temple in historic Asakusa',
          location: { name: 'Senso-ji Temple', address: 'Asakusa, Tokyo, Japan', lat: 35.7148, lng: 139.7967 },
          category: 'activity',
          duration: 120,
          cost: 5
        },
        {
          id: 6,
          time: '12:00',
          title: 'Sushi Lunch at Tsukiji',
          description: 'Fresh sushi at the famous fish market',
          location: { name: 'Tsukiji Market', address: 'Tsukiji, Tokyo, Japan', lat: 35.6654, lng: 139.7707 },
          category: 'restaurant',
          duration: 60,
          cost: 40
        }
      ]
    }
  ]
};

export default function PlanEditorPage({ params }: { params: { id: string } }) {
  const [trip, setTrip] = useState(mockTrip);
  const [selectedDay, setSelectedDay] = useState(1);
  const [isEditing, setIsEditing] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [mapView, setMapView] = useState('overview');
  const [lastSaved, setLastSaved] = useState(new Date());
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  const mapRef = useRef(null);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Auto-save functionality
  useEffect(() => {
    const interval = setInterval(() => {
      setLastSaved(new Date());
    }, 30000); // Auto-save every 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Close mobile sidebar when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: Event) => {
      if (isMobileSidebarOpen && !(event.target as Element)?.closest('.mobile-sidebar-container')) {
        setIsMobileSidebarOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isMobileSidebarOpen]);

  const handleTitleEdit = (newTitle: string) => {
    setTrip(prev => ({ ...prev, title: newTitle }));
  };

  const handleAddActivity = (dayIndex: number) => {
    const newActivity = {
      id: Date.now(),
      time: '12:00',
      title: 'New Activity',
      description: 'Add description...',
      location: { name: 'Location', address: '', lat: 0, lng: 0 },
      category: 'activity',
      duration: 60,
      cost: 0
    };

    setTrip(prev => ({
      ...prev,
      days: prev.days.map((day, index) => 
        index === dayIndex 
          ? { ...day, items: [...day.items, newActivity] }
          : day
      )
    }));
  };

  const handleDeleteActivity = (dayIndex: number, itemId: number) => {
    setTrip(prev => ({
      ...prev,
      days: prev.days.map((day, index) => 
        index === dayIndex 
          ? { ...day, items: day.items.filter(item => item.id !== itemId) }
          : day
      )
    }));
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'activity': return 'bg-blue-500';
      case 'restaurant': return 'bg-red-500';
      case 'accommodation': return 'bg-green-500';
      case 'transport': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'activity': return '🎯';
      case 'restaurant': return '🍽️';
      case 'accommodation': return '🏨';
      case 'transport': return '🚗';
      default: return '📍';
    }
  };

  const calculateDayStats = (day: any) => {
    const totalCost = day.items.reduce((sum: number, item: any) => sum + item.cost, 0);
    const totalDuration = day.items.reduce((sum: number, item: any) => sum + item.duration, 0);
    return { totalCost, totalDuration: Math.round(totalDuration / 60 * 10) / 10 };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Mobile Menu Button */}
            <div className="lg:hidden">
              <button
                onClick={() => setIsMobileSidebarOpen(true)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
                aria-label="Open sidebar"
              >
                <Menu className="h-5 w-5 text-gray-600" />
              </button>
            </div>

            {/* Title */}
            <div className="flex items-center space-x-4 flex-1 lg:flex-initial">
              {isEditing ? (
                <Input
                  value={trip.title}
                  onChange={(e) => handleTitleEdit(e.target.value)}
                  onBlur={() => setIsEditing(false)}
                  onKeyPress={(e) => e.key === 'Enter' && setIsEditing(false)}
                  className="text-lg lg:text-xl font-bold border-none p-0 h-auto focus:ring-0 max-w-xs lg:max-w-none"
                  autoFocus
                />
              ) : (
                <h1 
                  className="text-lg lg:text-xl font-bold text-gray-900 cursor-pointer hover:text-orange-500 truncate"
                  onClick={() => setIsEditing(true)}
                >
                  {trip.title}
                </h1>
              )}
              <Badge variant="outline" className="hidden sm:inline-flex">{trip.status}</Badge>
            </div>

            {/* Collaborators - Hidden on mobile */}
            <div className="hidden lg:flex items-center space-x-4">
              <div className="flex -space-x-2">
                {trip.collaborators.map((collaborator) => (
                  <img
                    key={collaborator.id}
                    src={collaborator.avatar}
                    alt={collaborator.name}
                    className="w-8 h-8 rounded-full border-2 border-white"
                    title={collaborator.name}
                  />
                ))}
                <button className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-gray-600 hover:bg-gray-300">
                  <Plus className="h-4 w-4" />
                </button>
              </div>

              {/* Actions */}
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {/* Mobile Actions */}
            <div className="lg:hidden">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-5 w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem>
                    <Share2 className="mr-2 h-4 w-4" />
                    Share
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Download className="mr-2 h-4 w-4" />
                    Export
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    Settings
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Trip Info - Mobile optimized */}
          <div className="flex items-center justify-between py-3 border-t border-gray-100 lg:py-4">
            <div className="flex items-center space-x-3 lg:space-x-6 text-xs lg:text-sm text-gray-600 overflow-x-auto">
              <div className="flex items-center space-x-1 whitespace-nowrap">
                <Calendar className="h-3 w-3 lg:h-4 lg:w-4" />
                <span className="hidden sm:inline">{trip.startDate} - {trip.endDate}</span>
                <span className="sm:hidden">7 days</span>
              </div>
              <div className="flex items-center space-x-1 whitespace-nowrap">
                <MapPin className="h-3 w-3 lg:h-4 lg:w-4" />
                <span>{trip.destination}</span>
              </div>
              <div className="flex items-center space-x-1 whitespace-nowrap">
                <Users className="h-3 w-3 lg:h-4 lg:w-4" />
                <span>{trip.collaborators.length + 1} travelers</span>
              </div>
            </div>
            <div className="flex items-center space-x-2 lg:space-x-4 text-xs lg:text-sm text-gray-500">
              <div className="flex items-center space-x-1">
                <Save className="h-3 w-3" />
                <span className="hidden sm:inline">Saved {lastSaved.toLocaleTimeString()}</span>
                <span className="sm:hidden">Saved</span>
              </div>
              <div className="flex items-center space-x-1">
                <Eye className="h-3 w-3" />
                <span>127</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-8">
          {/* Map Panel */}
          <div className="lg:col-span-2 order-2 lg:order-1">
            <Card className="h-[400px] lg:h-[600px] p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Interactive Map</h2>
                <div className="flex items-center space-x-2">
                  <Button
                    variant={mapView === 'overview' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setMapView('overview')}
                  >
                    Overview
                  </Button>
                  <Button
                    variant={mapView === 'day' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setMapView('day')}
                  >
                    Day {selectedDay}
                  </Button>
                </div>
              </div>
              
              {/* Map Placeholder */}
              <div 
                ref={mapRef}
                className="w-full h-full bg-gradient-to-br from-blue-100 to-green-100 rounded-lg flex items-center justify-center relative overflow-hidden"
              >
                {/* Mock map markers */}
                <div className="absolute inset-0">
                  {trip.days[selectedDay - 1]?.items.map((item, index) => (
                    <div
                      key={item.id}
                      className={`absolute w-6 h-6 lg:w-8 lg:h-8 ${getCategoryColor(item.category)} rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg cursor-pointer hover:scale-110 transition-transform`}
                      style={{
                        top: `${20 + index * 15}%`,
                        left: `${30 + index * 10}%`
                      }}
                      title={item.title}
                    >
                      {index + 1}
                    </div>
                  ))}
                </div>
                <div className="text-center text-gray-600 z-10">
                  <MapPin className="h-8 lg:h-12 w-8 lg:w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-sm lg:text-lg font-medium">Interactive Map View</p>
                  <p className="text-xs lg:text-sm">Click markers to view activity details</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Timeline Panel */}
          <div className="order-1 lg:order-2">
            {/* Mobile Sidebar */}
            <AnimatePresence>
              {isMobileSidebarOpen && (
                <>
                  {/* Backdrop */}
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/50 z-50 lg:hidden"
                    onClick={() => setIsMobileSidebarOpen(false)}
                  />
                  
                  {/* Sidebar */}
                  <motion.div
                    initial={{ x: '-100%' }}
                    animate={{ x: 0 }}
                    exit={{ x: '-100%' }}
                    transition={{ type: 'tween', duration: 0.3 }}
                    className="mobile-sidebar-container fixed top-0 left-0 h-full w-80 max-w-[85vw] bg-white shadow-xl z-50 lg:hidden overflow-y-auto"
                  >
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">Daily Timeline</h3>
                        <button
                          onClick={() => setIsMobileSidebarOpen(false)}
                          className="p-2 rounded-lg hover:bg-gray-100"
                        >
                          <X className="h-5 w-5" />
                        </button>
                      </div>
                      <TimelineContent />
                    </div>
                  </motion.div>
                </>
              )}
            </AnimatePresence>

            {/* Desktop Timeline */}
            <div className="hidden lg:block space-y-6">
              <TimelineContent />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  function TimelineContent() {
    return (
      <>
        <Card className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Timeline</h3>
          <Tabs value={selectedDay.toString()} onValueChange={(value) => setSelectedDay(parseInt(value))}>
            <TabsList className="grid w-full grid-cols-2">
              {trip.days.map((day) => (
                <TabsTrigger key={day.day} value={day.day.toString()}>
                  Day {day.day}
                </TabsTrigger>
              ))}
            </TabsList>
            
            {trip.days.map((day) => {
              const stats = calculateDayStats(day);
              return (
                <TabsContent key={day.day} value={day.day.toString()} className="mt-4">
                  <div className="space-y-4">
                    {/* Day Header */}
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold text-gray-900">{day.theme}</h4>
                      <p className="text-sm text-gray-600 mb-3">{day.date}</p>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-medium text-gray-900">${stats.totalCost}</div>
                          <div className="text-gray-600">Total Cost</div>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{stats.totalDuration}h</div>
                          <div className="text-gray-600">Duration</div>
                        </div>
                      </div>
                    </div>

                    {/* Activities */}
                    <div className="space-y-3">
                      <AnimatePresence>
                        {day.items.map((item, index) => (
                          <motion.div
                            key={item.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -20 }}
                            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow group"
                          >
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0">
                                <div className={`w-6 h-6 lg:w-8 lg:h-8 ${getCategoryColor(item.category)} rounded-full flex items-center justify-center text-white text-xs font-bold`}>
                                  {index + 1}
                                </div>
                              </div>
                              
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between">
                                  <h5 className="text-sm font-medium text-gray-900 truncate">
                                    {item.title}
                                  </h5>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0">
                                        <MoreHorizontal className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem>
                                        <Edit className="mr-2 h-4 w-4" />
                                        Edit
                                      </DropdownMenuItem>
                                      <DropdownMenuItem 
                                        className="text-red-600"
                                        onClick={() => handleDeleteActivity(day.day - 1, item.id)}
                                      >
                                        <Trash2 className="mr-2 h-4 w-4" />
                                        Delete
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                                
                                <div className="flex items-center space-x-2 text-xs text-gray-500 mt-1">
                                  <Clock className="h-3 w-3" />
                                  <span>{item.time}</span>
                                  <span>•</span>
                                  <span>{item.duration} min</span>
                                  <span>•</span>
                                  <span>${item.cost}</span>
                                </div>
                                
                                <p className="text-xs text-gray-600 mt-2 line-clamp-2">{item.description}</p>
                                <p className="text-xs text-gray-500 mt-1 truncate">{item.location.name}</p>
                              </div>
                              
                              <div className="flex-shrink-0 opacity-0 group-hover:opacity-100">
                                <GripVertical className="h-4 w-4 text-gray-400 cursor-move" />
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </AnimatePresence>
                    </div>

                    {/* Add Activity Button */}
                    <Button
                      variant="outline"
                      className="w-full border-dashed"
                      onClick={() => handleAddActivity(day.day - 1)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Activity
                    </Button>
                  </div>
                </TabsContent>
              );
            })}
          </Tabs>
        </Card>

        {/* Trip Summary */}
        <Card className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Trip Summary</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Activities</span>
              <span className="font-medium">{trip.days.reduce((sum, day) => sum + day.items.length, 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Estimated Cost</span>
              <span className="font-medium">${trip.days.reduce((sum, day) => sum + calculateDayStats(day).totalCost, 0)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Duration</span>
              <span className="font-medium">{trip.days.length} days</span>
            </div>
            <div className="pt-3 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span>Activities</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span>Restaurants</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span>Hotels</span>
                </div>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span>Transport</span>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </>
    );
  }
}