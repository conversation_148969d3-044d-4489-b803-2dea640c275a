"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowRight, CheckCircle, Mail, Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface WaitlistSignupProps {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary';
  className?: string;
  showBenefits?: boolean;
}

export function WaitlistSignup({ 
  size = 'md', 
  variant = 'primary',
  className = '',
  showBenefits = false 
}: WaitlistSignupProps) {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    // Basic email validation
    if (!email || !email.includes('@')) {
      setError('Please enter a valid email address');
      setIsLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/waitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Something went wrong');
      }

      setIsSubmitted(true);
      setEmail('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // Responsive styling based on size
  const containerClasses = {
    sm: 'space-y-2',
    md: 'space-y-3',
    lg: 'space-y-4'
  };

  const inputClasses = {
    sm: 'h-10 text-sm px-3',
    md: 'h-12 text-base px-4', 
    lg: 'h-14 text-lg px-5'
  };

  const buttonClasses = {
    sm: 'h-10 px-4 text-sm',
    md: 'h-12 px-6 text-base',
    lg: 'h-14 px-8 text-lg'
  };

  return (
    <div className={`w-full max-w-md mx-auto ${containerClasses[size]} ${className}`}>
      <AnimatePresence mode="wait">
        {!isSubmitted ? (
          <motion.form
            key="form"
            initial={{ opacity: 1 }}
            exit={{ opacity: 0, y: -10 }}
            onSubmit={handleSubmit}
            className="space-y-3"
          >
            {/* Email Input and Button - Stack on mobile, side-by-side on larger screens */}
            <div className="flex flex-col gap-3 sm:flex-row sm:gap-2">
              <div className="flex-1 min-w-0">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`
                    w-full ${inputClasses[size]}
                    border-2 border-gray-200 
                    focus:border-orange-500 focus:ring-orange-500/20 focus:ring-4
                    rounded-xl transition-all duration-200
                    placeholder:text-gray-400
                    ${variant === 'secondary' ? 'bg-white/90 backdrop-blur-sm' : 'bg-white'}
                  `}
                  disabled={isLoading}
                />
              </div>
              <Button
                type="submit"
                disabled={isLoading || !email}
                className={`
                  ${buttonClasses[size]} 
                  ${variant === 'primary' 
                    ? 'bg-orange-500 hover:bg-orange-600 text-white shadow-lg shadow-orange-500/25' 
                    : 'bg-white text-orange-500 hover:bg-gray-50 border-2 border-white shadow-lg'
                  } 
                  font-semibold rounded-xl transition-all duration-200 
                  hover:scale-105 hover:shadow-xl
                  disabled:opacity-50 disabled:hover:scale-100 disabled:cursor-not-allowed
                  group relative overflow-hidden
                  min-w-fit whitespace-nowrap
                `}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="hidden sm:inline">Joining...</span>
                    <span className="sm:hidden">...</span>
                  </div>
                ) : (
                  <>
                    <span className="hidden sm:inline">Join Waitlist</span>
                    <span className="sm:hidden">Join</span>
                    <ArrowRight className={`${size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'} ml-2 group-hover:translate-x-1 transition-transform`} />
                  </>
                )}
              </Button>
            </div>
            
            {/* Error Message */}
            <AnimatePresence>
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="flex items-center text-red-500 text-sm bg-red-50 px-3 py-2 rounded-lg"
                >
                  <div className="h-2 w-2 bg-red-500 rounded-full mr-2 flex-shrink-0"></div>
                  {error}
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Benefits */}
            {showBenefits && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="text-sm text-gray-600 space-y-2 pt-2"
              >
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Get early access when we launch</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Exclusive beta features and 50% discount</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>No spam, unsubscribe anytime</span>
                </div>
              </motion.div>
            )}
          </motion.form>
        ) : (
          <motion.div
            key="success"
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="text-center space-y-4 py-4"
          >
            <div className="flex justify-center">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, duration: 0.5, type: "spring" }}
                className="bg-green-100 rounded-full p-4"
              >
                <Mail className="h-8 w-8 text-green-600" />
              </motion.div>
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-gray-900">You're on the list! 🎉</h3>
              <p className="text-sm text-gray-600 leading-relaxed">
                We've saved your spot and will notify you as soon as TravelViz launches. 
                <br className="hidden sm:block" />
                Thanks for joining our journey!
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsSubmitted(false);
                setEmail('');
                setError('');
              }}
              className="text-sm font-medium hover:bg-gray-50 transition-colors"
            >
              Add another email
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
} 