"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.APP_CONFIG = exports.DATE_FORMATS = exports.DEFAULT_PAGINATION = exports.ERROR_CODES = exports.HTTP_STATUS = exports.API_ENDPOINTS = void 0;
// API endpoints
exports.API_ENDPOINTS = {
    USERS: '/api/users',
    TRIPS: '/api/trips',
    AUTH: '/api/auth',
    UPLOAD: '/api/upload',
};
// HTTP status codes
exports.HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500,
};
// Error codes
exports.ERROR_CODES = {
    VALIDATION_ERROR: 'VALIDATION_ERROR',
    UNAUTHORIZED: 'UNAUTHORIZED',
    FORBIDDEN: 'FORBIDDEN',
    NOT_FOUND: 'NOT_FOUND',
    INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
};
// Default pagination
exports.DEFAULT_PAGINATION = {
    page: 1,
    limit: 10,
    maxLimit: 100,
};
// Date formats
exports.DATE_FORMATS = {
    ISO: 'YYYY-MM-DDTHH:mm:ssZ',
    SHORT: 'YYYY-MM-DD',
    LONG: 'MMMM DD, YYYY',
};
// App configuration
exports.APP_CONFIG = {
    NAME: 'TravelViz',
    VERSION: '1.0.0',
    DESCRIPTION: 'AI-powered travel planning platform',
};
