"use client";

import { motion } from 'framer-motion';
import { 
  Bot, 
  Map, 
  Share2, 
  Download, 
  Lightbulb, 
  Users,
  Zap,
  ExternalLink,
  Copy,
  Globe,
  DollarSign,
  Bell,
  Eye,
  MessageCircle,
  Calendar,
  Wifi,
  TrendingDown
} from 'lucide-react';

const features = [
  {
    icon: Bo<PERSON>,
    title: "AI-to-Itinerary Magic",
    description: "Seamless AI conversation → bookable itinerary in 30 seconds. Paste ChatGPT plans and watch them transform into visual masterpieces.",
    color: "from-blue-500 to-blue-600",
    badge: "AI-First",
    highlights: ["Instant AI parsing", "Visual timeline", "Smart scheduling"]
  },
  {
    icon: DollarSign,
    title: "Smart Budget Tracking",
    description: "Automatic price drop alerts, budget tracking within itinerary, and find deals on AI-suggested places. Never overpay again.",
    color: "from-green-500 to-green-600",
    badge: "Money Saver",
    highlights: ["Price drop alerts", "Budget tracking", "Deal finder"]
  },
  {
    icon: Users,
    title: "Collaborative Planning",
    description: "One link to share entire trip plan. See who has viewed/approved plans, collaborative editing, and group booking coordination.",
    color: "from-purple-500 to-purple-600",
    badge: "Team Power",
    highlights: ["Real-time editing", "Approval tracking", "Group coordination"]
  },
  {
    icon: Share2,
    title: "Viral Sharing System",
    description: "Every trip becomes a shareable masterpiece. Share costs with travel partners and inspire others with your adventures.",
    color: "from-pink-500 to-pink-600",
    badge: "Social",
    highlights: ["Cost sharing", "Public templates", "Community inspiration"]
  },
  {
    icon: Wifi,
    title: "Offline-First Design",
    description: "Full offline access during trips. Download maps, itineraries, and all trip details for seamless travel without internet.",
    color: "from-indigo-500 to-indigo-600",
    badge: "Always Available",
    highlights: ["Offline maps", "Cached itineraries", "No internet needed"]
  },
  {
    icon: Map,
    title: "Interactive Visual Maps",
    description: "Visualize your entire journey with custom markers, routes, and location details on beautiful interactive maps.",
    color: "from-orange-500 to-orange-600",
    badge: "Visual",
    highlights: ["Interactive routes", "Custom markers", "Location details"]
  }
];

const keyBenefits = [
  {
    icon: TrendingDown,
    title: "Save Money Automatically",
    stats: "Average $400 saved per trip",
    features: [
      "Automatic price drop alerts for flights & hotels",
      "Budget tracking within your itinerary",
      "AI finds deals on suggested places",
      "Share costs transparently with travel partners"
    ]
  },
  {
    icon: Users,
    title: "Plan Together Seamlessly",
    stats: "10x faster group planning",
    features: [
      "One link to share entire trip plan",
      "See who has viewed and approved plans",
      "Real-time collaborative editing",
      "Coordinate group bookings effortlessly"
    ]
  },
  {
    icon: Zap,
    title: "AI-Powered Efficiency",
    stats: "30 seconds to visual itinerary",
    features: [
      "Seamless AI conversation → bookable itinerary",
      "Easy sharing with your travel group",
      "Price tracking without manual checking",
      "Full offline access during trips"
    ]
  }
];

export function FeatureGrid() {
  return (
    <section id="features" className="py-12 sm:py-16 lg:py-20 bg-gray-50 safe-area-inset-left safe-area-inset-right">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12 sm:mb-16"
        >
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-3 sm:mb-4 px-4 sm:px-0">
            The Only AI Travel Planner That Actually Saves You Money & Time
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto px-4 sm:px-0 mb-6">
            Stop juggling 15+ tabs and losing AI conversations. Transform any travel plan into a money-saving, collaborative masterpiece.
          </p>
        </motion.div>

        {/* Key Benefits Section */}
        <div className="mb-16 sm:mb-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-8">
              Why Travelers Choose TravelViz
            </h3>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {keyBenefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.2, duration: 0.6 }}
                className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1"
              >
                <div className="text-center mb-6">
                  <div className="inline-flex p-4 rounded-2xl bg-gradient-to-r from-orange-500 to-pink-500 mb-4">
                    <benefit.icon className="h-8 w-8 text-white" />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-2">{benefit.title}</h4>
                  <div className="text-2xl font-bold text-orange-500 mb-4">{benefit.stats}</div>
                </div>
                
                <ul className="space-y-3">
                  {benefit.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-start">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                      <span className="text-gray-700 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Detailed Features Grid */}
        <div className="mb-16 sm:mb-20">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-12"
          >
            <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-4">
              Every Feature Built to Save You Money & Time
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              No fluff, no gimmicks. Just powerful features that solve real travel planning problems.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1, duration: 0.6 }}
                className="group"
              >
                <div className="bg-white rounded-xl p-4 sm:p-6 lg:p-8 shadow-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-full touch-spacing relative">
                  {feature.badge && (
                    <div className="absolute top-3 sm:top-4 right-3 sm:right-4">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        {feature.badge}
                      </span>
                    </div>
                  )}
                  
                  <div className={`inline-flex p-2 sm:p-3 rounded-lg bg-gradient-to-r ${feature.color} mb-3 sm:mb-4 lg:mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <feature.icon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                  </div>
                  
                  <h3 className="text-base sm:text-lg lg:text-xl font-bold text-gray-900 mb-2 sm:mb-3 pr-8">
                    {feature.title}
                  </h3>
                  
                  <p className="text-gray-600 leading-relaxed text-sm sm:text-base mb-3 sm:mb-4">
                    {feature.description}
                  </p>

                  {feature.highlights && (
                    <div className="space-y-1 sm:space-y-2">
                      {feature.highlights.map((highlight, idx) => (
                        <div key={idx} className="flex items-center text-xs text-gray-500">
                          <div className="w-1 h-1 bg-orange-500 rounded-full mr-2 flex-shrink-0"></div>
                          <span className="leading-relaxed">{highlight}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Comparison Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="mt-16 sm:mt-20"
        >
          <div className="bg-white rounded-2xl p-4 sm:p-6 lg:p-8 shadow-sm">
            <h3 className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 mb-6 text-center">
              TravelViz vs Traditional Planning
            </h3>
            
            {/* Mobile Stacked Layout */}
            <div className="lg:hidden space-y-4">
              {[
                {
                  feature: "AI conversation to itinerary",
                  travelviz: "✅ 30 seconds",
                  traditional: "❌ 3+ hours manual work"
                },
                {
                  feature: "Automatic price tracking",
                  travelviz: "✅ Real-time alerts",
                  traditional: "❌ Manual checking"
                },
                {
                  feature: "Group collaboration",
                  travelviz: "✅ Real-time editing",
                  traditional: "❌ Email chaos"
                },
                {
                  feature: "Offline access",
                  travelviz: "✅ Full functionality",
                  traditional: "❌ Internet required"
                },
                {
                  feature: "Budget tracking",
                  travelviz: "✅ Automatic & shared",
                  traditional: "❌ Spreadsheet hell"
                }
              ].map((item, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-4 border border-gray-200">
                  <h4 className="font-medium text-gray-900 mb-3 text-sm">{item.feature}</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-orange-50 rounded-lg border border-orange-200">
                      <span className="text-xs font-medium text-orange-600">TravelViz</span>
                      <span className="text-xs text-gray-900">{item.travelviz}</span>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-100 rounded-lg border border-gray-300">
                      <span className="text-xs font-medium text-gray-600">Traditional</span>
                      <span className="text-xs text-gray-700">{item.traditional}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            {/* Desktop Table Layout */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Feature</th>
                    <th className="text-center py-3 px-4 font-medium text-orange-600">TravelViz</th>
                    <th className="text-center py-3 px-4 font-medium text-gray-600">Traditional Planning</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-100">
                  <tr>
                    <td className="py-3 px-4 text-gray-700">AI conversation to itinerary</td>
                    <td className="py-3 px-4 text-center">✅ 30 seconds</td>
                    <td className="py-3 px-4 text-center">❌ 3+ hours manual work</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-gray-700">Automatic price tracking</td>
                    <td className="py-3 px-4 text-center">✅ Real-time alerts</td>
                    <td className="py-3 px-4 text-center">❌ Manual checking</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-gray-700">Group collaboration</td>
                    <td className="py-3 px-4 text-center">✅ Real-time editing</td>
                    <td className="py-3 px-4 text-center">❌ Email chaos</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-gray-700">Offline access</td>
                    <td className="py-3 px-4 text-center">✅ Full functionality</td>
                    <td className="py-3 px-4 text-center">❌ Internet required</td>
                  </tr>
                  <tr>
                    <td className="py-3 px-4 text-gray-700">Budget tracking</td>
                    <td className="py-3 px-4 text-center">✅ Automatic & shared</td>
                    <td className="py-3 px-4 text-center">❌ Spreadsheet hell</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </motion.div>

        {/* Social Proof CTA */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ delay: 1, duration: 0.6 }}
          className="mt-12 sm:mt-16 lg:mt-20 mobile-card bg-gradient-to-r from-orange-500 to-pink-500 rounded-2xl p-6 sm:p-8 md:p-12 text-white text-center"
        >
          <div className="max-w-3xl mx-auto">
            <div className="flex items-center justify-center mb-4 sm:mb-6">
              <Zap className="h-8 w-8 sm:h-12 sm:w-12 text-yellow-300" />
            </div>
            <h3 className="text-xl sm:text-2xl md:text-3xl font-bold mb-3 sm:mb-4">
              Join Travelers Saving Money & Time
            </h3>
            <p className="text-base sm:text-lg md:text-xl mb-6 sm:mb-8 text-white/90">
              "I saved $600 on my Japan trip and planned it in 30 minutes instead of 20+ hours. 
              Our Tokyo dinners? Paid for by price alerts." - Wendi Griffin
            </p>
            
            {/* Trust Indicators */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
              <div className="text-center">
                <div className="text-base sm:text-lg lg:text-xl font-bold">$400+</div>
                <div className="text-xs sm:text-sm text-white/80">Total saved per users</div>
              </div>
              <div className="text-center">
                <div className="text-base sm:text-lg lg:text-xl font-bold">4.9★</div>
                <div className="text-xs sm:text-sm text-white/80">Average rating</div>
              </div>
              <div className="text-center">
                <div className="text-base sm:text-lg lg:text-xl font-bold">30 sec</div>
                <div className="text-xs sm:text-sm text-white/80">AI to itinerary</div>
              </div>
              <div className="text-center">
                <div className="text-base sm:text-lg lg:text-xl font-bold">10 Hours</div>
                <div className="text-xs sm:text-sm text-white/80">Total saved per users</div>
              </div>
            </div>
            
            <div className="flex justify-center">
              <button className="w-full sm:w-auto bg-white text-orange-500 hover:bg-gray-100 px-6 sm:px-8 py-3 sm:py-4 rounded-xl font-semibold transition-all duration-200 hover:scale-105 flex items-center justify-center min-h-[48px] touch-spacing">
                <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 mr-2" />
                Start Saving Money Free
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}