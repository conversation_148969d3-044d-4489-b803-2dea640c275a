import { NextRequest, NextResponse } from 'next/server';
import { waitlistAPI, type WaitlistEntry } from '@/lib/supabase';

// Email validation regex
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Storage Options:
// 1. SUPABASE - Full database with user management, analytics, etc.
// 2. RESEND - Email service with simple contact storage 
// 3. AIRTABLE - Simple spreadsheet storage, easy to manage
// 4. GOOGLE_SHEETS - Free option via Google Sheets API

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, name, source = 'web', referral_code, metadata = {} } = body;

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Validate email format
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const emailExists = await waitlistAPI.checkEmailExists(email);
    if (emailExists) {
      return NextResponse.json(
        { error: 'Email already registered', code: 'EMAIL_EXISTS' },
        { status: 409 }
      );
    }

    // Add user agent and IP to metadata
    const userAgent = request.headers.get('user-agent') || '';
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded?.split(',')[0] || request.headers.get('x-real-ip') || 'unknown';

    const waitlistData: Omit<WaitlistEntry, 'id' | 'created_at' | 'updated_at'> = {
      email: email.toLowerCase().trim(),
      name: name?.trim() || null,
      source,
      referral_code: referral_code || null,
      metadata: {
        ...metadata,
        userAgent,
        ip,
        timestamp: new Date().toISOString(),
      },
      status: 'active'
    };

    // Add to waitlist
    const result = await waitlistAPI.addToWaitlist(waitlistData);

    return NextResponse.json({
      success: true,
      message: 'Successfully added to waitlist!',
      data: {
        id: result.id,
        email: result.email,
        created_at: result.created_at
      }
    }, { status: 201 });

  } catch (error: any) {
    console.error('Waitlist signup error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to add to waitlist',
        message: error.message || 'Internal server error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get waitlist stats (public endpoint for showing total signups)
    const stats = await waitlistAPI.getWaitlistStats();
    
    return NextResponse.json({
      success: true,
      data: {
        total: stats.total,
        // Only expose total count for public stats
      }
    });

  } catch (error: any) {
    console.error('Waitlist stats error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to get waitlist stats',
        message: error.message || 'Internal server error'
      },
      { status: 500 }
    );
  }
}

// STORAGE IMPLEMENTATIONS (choose one to implement)

// 1. SUPABASE STORAGE
async function storeInSupabase(email: string) {
  // Uncomment and configure when ready:
  /*
  const { createClient } = require('@supabase/supabase-js');
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );

  const { data, error } = await supabase
    .from('waitlist')
    .insert([
      {
        email,
        created_at: new Date().toISOString(),
        status: 'pending'
      }
    ]);

  if (error) throw error;
  return data;
  */
}

// 2. RESEND STORAGE
async function storeInResend(email: string) {
  // Uncomment and configure when ready:
  /*
  const { Resend } = require('resend');
  const resend = new Resend(process.env.RESEND_API_KEY);

  // Add to contact list
  await resend.contacts.create({
    email,
    first_name: '',
    last_name: '',
    audience_id: process.env.RESEND_AUDIENCE_ID!,
  });

  // Optional: Send welcome email
  await resend.emails.send({
    from: 'TravelViz <<EMAIL>>',
    to: email,
    subject: 'Welcome to TravelViz Waitlist!',
    html: `
      <h2>You're on the list! 🎉</h2>
      <p>Thanks for joining the TravelViz waitlist. We'll notify you as soon as we launch.</p>
      <p>In the meantime, feel free to explore our templates and see what we're building.</p>
    `
  });
  */
}

// 3. AIRTABLE STORAGE
async function storeInAirtable(email: string) {
  // Uncomment and configure when ready:
  /*
  const response = await fetch(`https://api.airtable.com/v0/${process.env.AIRTABLE_BASE_ID}/waitlist`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${process.env.AIRTABLE_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      records: [
        {
          fields: {
            email,
            timestamp: new Date().toISOString(),
            status: 'pending'
          }
        }
      ]
    })
  });

  if (!response.ok) {
    throw new Error('Failed to store in Airtable');
  }
  */
}

// 4. GOOGLE SHEETS STORAGE
async function storeInGoogleSheets(email: string) {
  // Uncomment and configure when ready:
  /*
  const { GoogleSpreadsheet } = require('google-spreadsheet');
  const doc = new GoogleSpreadsheet(process.env.GOOGLE_SHEET_ID);

  await doc.useServiceAccountAuth({
    client_email: process.env.GOOGLE_SERVICE_ACCOUNT_EMAIL,
    private_key: process.env.GOOGLE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  });

  await doc.loadInfo();
  const sheet = doc.sheetsByIndex[0];

  await sheet.addRow({
    email,
    timestamp: new Date().toISOString(),
    status: 'pending'
  });
  */
} 