# Next.js Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3001

# Supabase (Public keys for client-side)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Mapbox (Public token for client-side)
NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=your_mapbox_public_access_token

# Analytics & Monitoring
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=your_vercel_analytics_id
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=your_google_analytics_id

# Feature Flags
NEXT_PUBLIC_ENABLE_AI_CHAT=true
NEXT_PUBLIC_ENABLE_PRICE_TRACKING=true
NEXT_PUBLIC_ENABLE_OFFLINE_MAPS=true

# Environment
NEXT_PUBLIC_ENVIRONMENT=development

# Stripe (for subscriptions)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Social Auth (if using)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
NEXT_PUBLIC_FACEBOOK_APP_ID=your_facebook_app_id

# Sentry (Error tracking)
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# Development
NEXT_PUBLIC_DEBUG=false
