"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSuccessResponse = createSuccessResponse;
exports.createErrorResponse = createErrorResponse;
exports.isValidUUID = isValidUUID;
exports.formatDate = formatDate;
exports.parseDate = parseDate;
exports.slugify = slugify;
exports.capitalize = capitalize;
/**
 * Creates a successful API response
 */
function createSuccessResponse(data, message) {
    return {
        success: true,
        data,
        message,
    };
}
/**
 * Creates an error API response
 */
function createErrorResponse(error, message, code) {
    return {
        success: false,
        error,
        message,
        code,
    };
}
/**
 * Validates if a string is a valid UUID
 */
function isValidUUID(uuid) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
}
/**
 * Formats a date to ISO string
 */
function formatDate(date) {
    return date.toISOString();
}
/**
 * Parses a date from ISO string
 */
function parseDate(dateString) {
    return new Date(dateString);
}
/**
 * Slugifies a string for URLs
 */
function slugify(text) {
    return text
        .toString()
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^\w\-]+/g, '')
        .replace(/\-\-+/g, '-')
        .replace(/^-+/, '')
        .replace(/-+$/, '');
}
/**
 * Capitalizes first letter of a string
 */
function capitalize(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
}
