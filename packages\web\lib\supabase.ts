import { createClient } from '@supabase/supabase-js'

// Supabase configuration for TravelViz Waitlist
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error(
    'Missing Supabase environment variables. Please add NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY to your .env.local file.'
  )
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Waitlist types
export interface WaitlistEntry {
  id?: string
  email: string
  name?: string
  created_at?: string
  updated_at?: string
  source?: string
  referral_code?: string
  metadata?: Record<string, any>
  status?: 'active' | 'invited' | 'converted'
}

// Waitlist API functions
export const waitlistAPI = {
  // Add user to waitlist
  async addToWaitlist(data: Omit<WaitlistEntry, 'id' | 'created_at' | 'updated_at'>) {
    const { data: result, error } = await supabase
      .from('waitlist')
      .insert([data])
      .select()
      .single()

    if (error) {
      console.error('Error adding to waitlist:', error)
      throw error
    }

    return result
  },

  // Check if email exists in waitlist
  async checkEmailExists(email: string) {
    const { data, error } = await supabase
      .from('waitlist')
      .select('email')
      .eq('email', email)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking email:', error)
      throw error
    }

    return !!data
  },

  // Get waitlist stats (for admin use)
  async getWaitlistStats() {
    const { data, error } = await supabase
      .from('waitlist')
      .select('status')

    if (error) {
      console.error('Error getting waitlist stats:', error)
      throw error
    }

    const stats = {
      total: data.length,
      active: data.filter((entry: any) => entry.status === 'active').length,
      invited: data.filter((entry: any) => entry.status === 'invited').length,
      converted: data.filter((entry: any) => entry.status === 'converted').length,
    }

    return stats
  },

  // Get waitlist entries (for admin use)
  async getWaitlistEntries(limit = 100, offset = 0) {
    const { data, error } = await supabase
      .from('waitlist')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error getting waitlist entries:', error)
      throw error
    }

    return data
  }
} 