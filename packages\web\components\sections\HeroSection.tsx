"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Play, ArrowRight, Users, Heart, Zap, Compass, Globe, DollarSign, Clock, Bot, Share2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { VideoShowcase } from './VideoShowcase';
import { WaitlistSignup } from '@/components/WaitlistSignup';

export function HeroSection() {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16 pb-8">
      {/* Background */}
      <div className="absolute inset-0 hero-gradient"></div>
      <div className="absolute inset-0 bg-black/10"></div>
      
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-6 sm:space-y-8"
        >
          {/* Problem Statement Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-flex items-center space-x-2 bg-white/20 backdrop-blur-sm rounded-full px-3 py-2 sm:px-4 text-white text-xs sm:text-sm"
          >
            <Zap className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-300" />
            <span className="hidden sm:inline">Stop juggling 15+ tabs • Transform AI conversations into visual trips</span>
            <span className="sm:hidden">Stop juggling 15+ tabs</span>
          </motion.div>

          {/* Main Headline */}
          <div className="space-y-3 sm:space-y-4">
            <h1 className="responsive-text-hero font-bold text-white leading-tight px-2 sm:px-0">
              Turn AI Travel Plans into
              <br className="hidden sm:block" />
              <span className="sm:hidden"> </span>
              <span className="text-yellow-300">Money-Saving Adventures</span>
            </h1>
            <p className="responsive-text-subtitle text-white/90 max-w-3xl mx-auto leading-relaxed px-4 sm:px-0">
              <span className="block sm:hidden">Paste ChatGPT conversations, get visual itineraries with automatic price tracking and group collaboration in 30 seconds.</span>
              <span className="hidden sm:block">Effortlessly transform scattered travel ideas—from ChatGPT outputs to rough notes—into a single, stunning itinerary. Unlock automatic price tracking, manage your budget, and collaborate with your group, all in one place.</span>
            </p>
          </div>

          {/* Key Value Props */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-8 text-white/90"
          >
            <div className="flex items-center space-x-2">
              <DollarSign className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-300" />
              <span className="text-sm sm:text-base font-medium">Save $400+ per trip</span>
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-300" />
              <span className="text-sm sm:text-base font-medium">30-second AI import</span>
            </div>
            <div className="flex items-center space-x-2">
              <Share2 className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-300" />
              <span className="text-sm sm:text-base font-medium">One-click sharing</span>
            </div>
          </motion.div>

          {/* CTA Buttons - Better spacing and positioning */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.5 }}
            className="flex flex-col items-center space-y-4 px-4 sm:px-0 pt-4"
          >
            {/* Waitlist Signup - Full width on mobile, centered on desktop */}
            <div className="w-full max-w-lg">
              <WaitlistSignup 
                size="lg" 
                variant="secondary"
                className="w-full"
                showBenefits={false}
              />
            </div>
            
         
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="text-white/80 text-xs sm:text-sm space-y-3 pt-4"
          >
            <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-6 text-xs sm:text-sm">
              <span className="flex items-center">
                <span className="text-yellow-300 mr-1">✨</span>
                No credit card required
              </span>
              <span className="flex items-center">
                <span className="text-yellow-300 mr-1">💰</span>
                Average $400 saved per trip
              </span>
              <span className="flex items-center">
                <span className="text-yellow-300 mr-1">⚡</span>
                30-second setup
              </span>
            </div>
          </motion.div>

          {/* Video Showcase - Added more top margin */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1, duration: 0.8 }}
            className="pt-12 sm:pt-16"
          >
            <div className="w-full">
              <VideoShowcase />
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2, duration: 0.5 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 hidden sm:block"
      >
        <div className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-bounce"></div>
        </div>
      </motion.div>
    </section>
  );
}