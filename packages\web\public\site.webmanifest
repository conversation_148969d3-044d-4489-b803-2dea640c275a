{"name": "TravelViz travel planner: free AI planner and GPT itinerary", "short_name": "TravelViz", "description": "Effortlessly transform scattered travel ideas—from ChatGPT outputs to rough notes—into a single, stunning itinerary. Unlock automatic price tracking, manage your budget, and collaborate with your group, all in one place.", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#f97316", "orientation": "portrait-primary", "scope": "/", "lang": "en-US", "dir": "ltr", "categories": ["travel", "productivity", "planning", "ai", "maps", "collaboration"], "screenshots": [{"src": "/screenshot-wide.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "TravelViz Dashboard - Plan your perfect trip"}, {"src": "/screenshot-narrow.png", "sizes": "750x1334", "type": "image/png", "form_factor": "narrow", "label": "TravelViz Mobile - Travel planning on the go"}], "icons": [{"src": "/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "any"}, {"src": "/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any"}, {"src": "/icon-192-maskable.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "/icon-512-maskable.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}], "shortcuts": [{"name": "Create New Trip", "short_name": "New Trip", "description": "Start planning a new travel itinerary", "url": "/dashboard?action=create", "icons": [{"src": "/shortcut-new-trip.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Import from AI", "short_name": "AI Import", "description": "Import travel plans from ChatGPT or other AI", "url": "/upload", "icons": [{"src": "/shortcut-ai-import.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Browse Templates", "short_name": "Templates", "description": "Explore AI prompt templates for travel planning", "url": "/templates", "icons": [{"src": "/shortcut-templates.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}