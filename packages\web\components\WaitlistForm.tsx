"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Users, Mail, Check, AlertCircle, Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';

interface WaitlistFormProps {
  className?: string;
  source?: string;
  referralCode?: string;
}

export function WaitlistForm({ className = '', source = 'web', referralCode }: WaitlistFormProps) {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setStatus('error');
      setMessage('Please enter your email address');
      return;
    }

    setIsSubmitting(true);
    setStatus('idle');
    setMessage('');

    try {
      const response = await fetch('/api/waitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: email.trim(),
          name: name.trim() || undefined,
          source,
          referral_code: referralCode,
          metadata: {
            url: window.location.href,
            utm_source: new URLSearchParams(window.location.search).get('utm_source'),
            utm_medium: new URLSearchParams(window.location.search).get('utm_medium'),
            utm_campaign: new URLSearchParams(window.location.search).get('utm_campaign'),
          }
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        setMessage(data.message || 'Thanks for joining our waitlist!');
        setEmail('');
        setName('');
        
        // Track success event (you can add analytics here)
        if (typeof window !== 'undefined' && (window as any).gtag) {
          (window as any).gtag('event', 'waitlist_signup', {
            event_category: 'engagement',
            event_label: source
          });
        }
      } else {
        setStatus('error');
        if (data.code === 'EMAIL_EXISTS') {
          setMessage('You\'re already on our waitlist! We\'ll notify you soon.');
        } else {
          setMessage(data.error || 'Something went wrong. Please try again.');
        }
      }
    } catch (error) {
      console.error('Waitlist signup error:', error);
      setStatus('error');
      setMessage('Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (status === 'success') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
        className={`bg-green-50 border border-green-200 rounded-lg p-6 text-center ${className}`}
      >
        <div className="flex items-center justify-center w-12 h-12 bg-green-100 rounded-full mx-auto mb-4">
          <Check className="h-6 w-6 text-green-600" />
        </div>
        <h3 className="text-lg font-semibold text-green-900 mb-2">
          You're on the list!
        </h3>
        <p className="text-green-700 text-sm">
          {message}
        </p>
        <p className="text-green-600 text-xs mt-2">
          We'll email you as soon as TravelViz is ready!
        </p>
      </motion.div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-3">
          <div>
            <Input
              type="email"
              placeholder="Enter your email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full min-h-[48px] text-base"
              disabled={isSubmitting}
              required
            />
          </div>
          <div>
            <Input
              type="text"
              placeholder="Your name (optional)"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full min-h-[48px] text-base"
              disabled={isSubmitting}
            />
          </div>
        </div>

        <Button
          type="submit"
          className="w-full min-h-[48px] bg-orange-500 hover:bg-orange-600 text-white font-semibold"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Joining Waitlist...
            </>
          ) : (
            <>
              <Users className="h-4 w-4 mr-2" />
              Join Our Community Free
            </>
          )}
        </Button>

        {status === 'error' && message && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg"
          >
            <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
            <p className="text-red-700 text-sm">{message}</p>
          </motion.div>
        )}
      </form>

      <div className="text-center text-xs text-gray-500 space-y-1">
        <p>✨ No spam, ever • 🚀 Early access guaranteed</p>
        <p>💰 Free forever • ⚡ Be the first to save money on travel</p>
      </div>
    </div>
  );
} 