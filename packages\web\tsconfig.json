{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "composite": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/app/*": ["./app/*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/hooks/*": ["./hooks/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"], "references": [{"path": "../shared"}]}