"use client";

import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>R<PERSON> } from 'lucide-react';
import { WaitlistSignup } from './WaitlistSignup';

interface ComingSoonProps {
  title: string;
  description: string;
  features?: string[];
  showWaitlist?: boolean;
  className?: string;
}

export function ComingSoon({ 
  title, 
  description, 
  features = [],
  showWaitlist = true,
  className = ''
}: ComingSoonProps) {
  return (
    <div className={`min-h-[60vh] flex items-center justify-center ${className}`}>
      <div className="max-w-2xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="space-y-6"
        >
          {/* Icon */}
          <div className="flex justify-center">
            <div className="bg-orange-100 rounded-full p-4">
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </div>

          {/* Title */}
          <div className="space-y-3">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900">
              {title}
            </h1>
            <p className="text-lg text-gray-600 max-w-xl mx-auto">
              {description}
            </p>
          </div>

          {/* Coming Soon Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
            className="inline-flex items-center space-x-2 bg-orange-100 text-orange-800 rounded-full px-4 py-2 text-sm font-medium"
          >
            <Sparkles className="h-4 w-4" />
            <span>Coming Soon</span>
          </motion.div>

          {/* Features List */}
          {features.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.5 }}
              className="bg-gray-50 rounded-xl p-6 text-left"
            >
              <h3 className="font-semibold text-gray-900 mb-4 text-center">
                What's Coming:
              </h3>
              <ul className="space-y-2">
                {features.map((feature, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <ArrowRight className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          )}

          {/* Waitlist Signup */}
          {showWaitlist && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              className="space-y-4"
            >
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-center space-x-2 mb-4">
                  <Bell className="h-5 w-5 text-orange-500" />
                  <h3 className="font-semibold text-gray-900">Get Notified When It's Ready</h3>
                </div>
                <WaitlistSignup 
                  size="md"
                  showBenefits={true}
                  className="max-w-md mx-auto"
                />
              </div>
            </motion.div>
          )}

          {/* Estimated Timeline */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="text-sm text-gray-500"
          >
            <p>We're working hard to bring you this feature. Stay tuned! 🚀</p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
} 