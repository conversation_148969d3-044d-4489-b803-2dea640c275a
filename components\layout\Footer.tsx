import Link from 'next/link';
import Image from 'next/image';
import { Twitter, Instagram, Mail, Github, Linkedin, Heart, ExternalLink } from 'lucide-react';

const footerSections = [
  {
    title: 'Product',
    links: [
      { name: 'Features', href: '/#features' },
      { name: 'Pricing', href: '/#pricing' },
      { name: 'Examples', href: '/examples' },
      { name: 'AI Assistant', href: '/ai-chat' },
      { name: 'Document Upload', href: '/upload' }
    ]
  },
  {
    title: 'Community',
    links: [
      { name: 'About Us', href: '/about' },
      { name: 'Contact', href: '/contact' },
      { name: 'Help Center', href: '/help' },
      { name: 'Join Community', href: '/signup' }
    ]
  },
  {
    title: 'Resources',
    links: [
      { name: 'Privacy Policy', href: '/privacy' },
      { name: 'Terms of Service', href: '/terms' },
      { name: 'Support', href: '/contact' }
    ]
  }
];

const socialLinks = [
  { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail },
  { name: 'Twitter', href: '#', icon: Twitter },
  { name: 'Instagram', href: '#', icon: Instagram }
];

export function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1 space-y-6">
            <div className="flex items-center space-x-2">
              <div>
                <Image 
                  src="/logo-small.svg" 
                  alt="TravelViz Logo" 
                  width={40} 
                  height={40}
                  className="w-10 h-10"
                />
              </div>
              <span className="text-2xl font-bold">TravelViz</span>
            </div>
            <p className="text-gray-300 text-sm leading-relaxed max-w-sm">
              An authentic travel companion built by travelers, for travelers. 
              Join our early community and help shape the future of travel experiences.
            </p>
            
            {/* Founder Attribution */}
            <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-pink-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  LZ
                </div>
                <div>
                  <p className="text-sm font-medium text-white">Built by Louis Zeng</p>
  
                </div>
              </div>
            </div>
            
            {/* Social Links */}
            <div className="flex space-x-4">
              {socialLinks.map((social) => (
                <a 
                  key={social.name}
                  href={social.href} 
                  className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg"
                  aria-label={`Contact us via ${social.name}`}
                  target={social.href.startsWith('mailto:') ? undefined : "_blank"}
                  rel={social.href.startsWith('mailto:') ? undefined : "noopener noreferrer"}
                >
                  <social.icon className="h-5 w-5" />
                </a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-4">
              <h3 className="font-semibold text-lg text-white">{section.title}</h3>
              <ul className="space-y-3">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <Link 
                      href={link.href} 
                      className="text-gray-300 hover:text-white transition-colors text-sm hover:underline"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="flex flex-col lg:flex-row justify-between items-center space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6">
              <p className="text-gray-400 text-sm">
                © 2024 TravelViz. Built with ❤️ by travelers, for travelers.
              </p>
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <div className="flex items-center space-x-1">
                  <Heart className="h-3 w-3 text-red-500" />
                  <span>Genuine passion for exploration</span>
                </div>
                <span>•</span>
                <span>Community-driven</span>
                <span>•</span>
                <span>No fake promises</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-6">
              {/* Trust Indicators */}
              <div className="flex items-center space-x-4 text-xs text-gray-400">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>Early community building</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}