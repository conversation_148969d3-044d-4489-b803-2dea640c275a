"use client";

import { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Search, Plus, Upload, FileText, Settings, LogOut, Crown } from 'lucide-react';

interface DashboardHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  onCreateTrip: () => void;
}

export function DashboardHeader({ searchQuery, onSearchChange, onCreateTrip }: DashboardHeaderProps) {
  const [user] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=100',
    plan: 'free',
    usage: {
      trips: 2,
      maxTrips: 3,
      aiGenerations: 7,
      maxAiGenerations: 10
    }
  });

  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div>
              <Image 
                src="/logo-small.svg" 
                alt="TravelViz Logo" 
                width={40} 
                height={40}
                className="w-10 h-10"
              />
            </div>
            <span className="text-xl font-bold text-gray-900">TravelViz</span>
          </Link>

          {/* Search Bar */}
          <div className="flex-1 max-w-lg mx-8">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search your trips..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="pl-10 pr-4 w-full"
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            {/* Quick Actions */}
            <div className="hidden md:flex items-center space-x-2">
              <Button onClick={onCreateTrip} className="btn-primary">
                <Plus className="h-4 w-4 mr-2" />
                New Trip
              </Button>
              
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
              
              <Button variant="outline" size="sm">
                <FileText className="h-4 w-4 mr-2" />
                Templates
              </Button>
            </div>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-80" align="end" forceMount>
                {/* User Info */}
                <div className="flex items-center space-x-2 p-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback>{user.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div className="space-y-1">
                    <p className="text-sm font-medium leading-none">{user.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">{user.email}</p>
                    <div className="flex items-center space-x-1">
                      {user.plan === 'free' ? (
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Free Plan</span>
                      ) : (
                        <span className="text-xs bg-orange-100 text-orange-600 px-2 py-1 rounded flex items-center">
                          <Crown className="h-3 w-3 mr-1" />
                          Premium
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                <DropdownMenuSeparator />

                {/* Usage Stats */}
                <div className="p-4 space-y-3">
                  <div>
                    <div className="flex justify-between text-xs text-gray-600 mb-1">
                      <span>Monthly Trips</span>
                      <span>{user.usage.trips}/{user.usage.maxTrips}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-orange-500 h-2 rounded-full" 
                        style={{ width: `${(user.usage.trips / user.usage.maxTrips) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between text-xs text-gray-600 mb-1">
                      <span>AI Generations</span>
                      <span>{user.usage.aiGenerations}/{user.usage.maxAiGenerations}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full" 
                        style={{ width: `${(user.usage.aiGenerations / user.usage.maxAiGenerations) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  {user.plan === 'free' && (
                    <Button className="w-full btn-primary text-xs" size="sm">
                      <Crown className="h-3 w-3 mr-1" />
                      Upgrade to Premium
                    </Button>
                  )}
                </div>

                <DropdownMenuSeparator />

                {/* Menu Items */}
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                
                <DropdownMenuItem>
                  <FileText className="mr-2 h-4 w-4" />
                  <span>Billing</span>
                </DropdownMenuItem>
                
                <DropdownMenuSeparator />
                
                <DropdownMenuItem className="text-red-600">
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
}