import type { ApiResponse, ErrorResponse } from './types';
/**
 * Creates a successful API response
 */
export declare function createSuccessResponse<T>(data: T, message?: string): ApiResponse<T>;
/**
 * Creates an error API response
 */
export declare function createErrorResponse(error: string, message?: string, code?: string): ErrorResponse;
/**
 * Validates if a string is a valid UUID
 */
export declare function isValidUUID(uuid: string): boolean;
/**
 * Formats a date to ISO string
 */
export declare function formatDate(date: Date): string;
/**
 * Parses a date from ISO string
 */
export declare function parseDate(dateString: string): Date;
/**
 * Slugifies a string for URLs
 */
export declare function slugify(text: string): string;
/**
 * Capitalizes first letter of a string
 */
export declare function capitalize(str: string): string;
