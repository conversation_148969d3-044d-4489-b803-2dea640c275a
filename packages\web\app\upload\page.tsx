"use client";

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  FileText, 
  Image, 
  X, 
  CheckCircle, 
  AlertCircle, 
  Sparkles,
  MapPin,
  Calendar,
  Clock,
  ArrowRight
} from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

const supportedFormats = [
  { type: 'PDF', icon: FileText, description: 'Travel itineraries, booking confirmations' },
  { type: 'Text', icon: FileText, description: 'ChatGPT outputs, travel notes' },
  { type: 'Images', icon: Image, description: 'Screenshots of travel plans' }
];

const processingSteps = [
  { id: 1, title: 'Uploading', description: 'Securely uploading your document' },
  { id: 2, title: 'Analyzing', description: 'AI is reading and understanding content' },
  { id: 3, title: 'Extracting', description: 'Finding locations, dates, and activities' },
  { id: 4, title: 'Organizing', description: 'Creating your visual itinerary' }
];

const mockExtractionResults = {
  title: "7 Days in Tokyo & Kyoto",
  destination: "Japan",
  duration: 7,
  startDate: "2024-04-15",
  endDate: "2024-04-22",
  activitiesFound: 23,
  locationsFound: 15,
  estimatedBudget: 1200,
  confidence: 95
};

interface UploadedFile {
  id: number;
  file: File;
  name: string;
  size: number;
  type: string;
  status: string;
}

interface ExtractionResults {
  title: string;
  destination: string;
  duration: number;
  startDate: string;
  endDate: string;
  activitiesFound: number;
  locationsFound: number;
  estimatedBudget: number;
  confidence: number;
}

export default function UploadPage() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [extractionResults, setExtractionResults] = useState<ExtractionResults | null>(null);
  const [processingProgress, setProcessingProgress] = useState(0);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => ({
      id: Date.now() + Math.random(),
      file,
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploaded'
    }));
    
    setUploadedFiles(prev => [...prev, ...newFiles]);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'text/plain': ['.txt'],
      'text/markdown': ['.md'],
      'image/*': ['.png', '.jpg', '.jpeg']
    },
    maxSize: 10 * 1024 * 1024, // 10MB
    multiple: true
  });

  const removeFile = (fileId: number) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const processFiles = async () => {
    if (uploadedFiles.length === 0) return;

    setIsProcessing(true);
    setCurrentStep(0);
    setProcessingProgress(0);

    // Simulate processing steps
    for (let i = 0; i < processingSteps.length; i++) {
      setCurrentStep(i);
      
      // Simulate progress within each step
      for (let progress = 0; progress <= 100; progress += 10) {
        setProcessingProgress((i * 100 + progress) / processingSteps.length);
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    // Show results
    setExtractionResults(mockExtractionResults);
    setIsProcessing(false);
  };

  const createTrip = () => {
    // Navigate to plan editor with extracted data
    window.location.href = '/plan/new?source=upload';
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <div className="p-2 bg-orange-500 rounded-lg">
                <Upload className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Document Upload</h1>
                <p className="text-sm text-gray-600">Transform any travel document into a visual itinerary</p>
              </div>
            </div>
            <Button variant="outline" onClick={() => window.history.back()}>
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {!extractionResults ? (
          <div className="space-y-8">
            {/* Supported Formats */}
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Upload Your Travel Documents
              </h2>
              <p className="text-gray-600 mb-8">
                We support multiple formats and can extract itineraries from any travel-related document
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                {supportedFormats.map((format, index) => (
                  <Card key={index} className="p-4 text-center">
                    <format.icon className="h-8 w-8 text-orange-500 mx-auto mb-2" />
                    <h3 className="font-semibold text-gray-900 mb-1">{format.type}</h3>
                    <p className="text-sm text-gray-600">{format.description}</p>
                  </Card>
                ))}
              </div>
            </div>

            {/* Upload Zone */}
            <Card className="p-8">
              <div
                {...getRootProps()}
                className={`border-2 border-dashed rounded-lg p-12 text-center cursor-pointer transition-all duration-200 ${
                  isDragActive 
                    ? 'border-orange-500 bg-orange-50' 
                    : 'border-gray-300 hover:border-orange-400 hover:bg-gray-50'
                }`}
              >
                <input {...getInputProps()} />
                <div className="space-y-4">
                  <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center ${
                    isDragActive ? 'bg-orange-500' : 'bg-gray-100'
                  }`}>
                    <Upload className={`h-8 w-8 ${isDragActive ? 'text-white' : 'text-gray-400'}`} />
                  </div>
                  
                  {isDragActive ? (
                    <div>
                      <p className="text-lg font-medium text-orange-600">Drop your files here</p>
                      <p className="text-gray-600">We'll process them instantly</p>
                    </div>
                  ) : (
                    <div>
                      <p className="text-lg font-medium text-gray-900">
                        Drag & drop your travel documents here
                      </p>
                      <p className="text-gray-600">or click to browse files</p>
                      <p className="text-sm text-gray-500 mt-2">
                        Supports PDF, TXT, MD, PNG, JPG (max 10MB each)
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </Card>

            {/* Uploaded Files */}
            <AnimatePresence>
              {uploadedFiles.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <Card className="p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Uploaded Files ({uploadedFiles.length})
                    </h3>
                    
                    <div className="space-y-3">
                      {uploadedFiles.map((file) => (
                        <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <FileText className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="font-medium text-gray-900">{file.name}</p>
                              <p className="text-sm text-gray-600">{formatFileSize(file.size)}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Ready
                            </Badge>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(file.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-6 flex justify-center">
                      <Button
                        onClick={processFiles}
                        className="btn-primary"
                        disabled={isProcessing}
                      >
                        {isProcessing ? (
                          <div className="flex items-center">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Processing...
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <Sparkles className="h-4 w-4 mr-2" />
                            Process Documents
                            <ArrowRight className="h-4 w-4 ml-2" />
                          </div>
                        )}
                      </Button>
                    </div>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Processing Status */}
            <AnimatePresence>
              {isProcessing && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <Card className="p-8 text-center">
                    <div className="space-y-6">
                      <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
                        <Sparkles className="h-8 w-8 text-orange-500 animate-pulse" />
                      </div>
                      
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-2">
                          {processingSteps[currentStep]?.title}
                        </h3>
                        <p className="text-gray-600">
                          {processingSteps[currentStep]?.description}
                        </p>
                      </div>

                      <div className="max-w-md mx-auto">
                        <Progress value={processingProgress} className="h-2" />
                        <p className="text-sm text-gray-500 mt-2">
                          {Math.round(processingProgress)}% complete
                        </p>
                      </div>

                      <div className="flex justify-center space-x-4">
                        {processingSteps.map((step, index) => (
                          <div
                            key={step.id}
                            className={`w-3 h-3 rounded-full ${
                              index <= currentStep ? 'bg-orange-500' : 'bg-gray-200'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ) : (
          /* Extraction Results */
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-8"
          >
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Extraction Complete!
              </h2>
              <p className="text-gray-600">
                We've successfully analyzed your documents and extracted the following information
              </p>
            </div>

            <Card className="p-8">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Trip Overview */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Trip Overview</h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-2xl font-bold text-gray-900">{extractionResults.title}</h4>
                      <p className="text-gray-600">{extractionResults.destination}</p>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{extractionResults.duration} days</p>
                          <p className="text-xs text-gray-600">Duration</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{extractionResults.locationsFound}</p>
                          <p className="text-xs text-gray-600">Locations</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Extraction Stats */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">What We Found</h3>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Activities & Events</span>
                      <span className="font-semibold text-gray-900">{extractionResults.activitiesFound}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Unique Locations</span>
                      <span className="font-semibold text-gray-900">{extractionResults.locationsFound}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Estimated Budget</span>
                      <span className="font-semibold text-gray-900">${extractionResults.estimatedBudget}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">Confidence Score</span>
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        {extractionResults.confidence}%
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200 flex justify-center space-x-4">
                <Button variant="outline" onClick={() => setExtractionResults(null)}>
                  Upload More Files
                </Button>
                <Button onClick={createTrip} className="btn-primary">
                  <Sparkles className="h-4 w-4 mr-2" />
                  Create Visual Itinerary
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              </div>
            </Card>
          </motion.div>
        )}
      </div>
    </div>
  );
}