# TravelViz Detailed MVP Execution Plan (v2.1)

## 🎯 Core Principle: Deliver a valuable, revenue-generating MVP by focusing on the core user journey: AI conversation -> Visual, Actionable Itinerary.

**Current Status:** The frontend is well-underway with a rich component library and page structure. The immediate priority is building the backend services and integrating them to bring the application to life.

---

## Phase 1: Backend & Core Integration (Days 1-15)

### TASK 001: Backend Foundation & Database Setup

**Owner**: Tech Lead
**Goal**: Establish a secure and scalable backend service and database schema.

*   **Sub-Task 1.1**: Initialize a new Node.js/Express project using TypeScript.
    *   **Details**: Set up with a standard project structure, including `tsconfig.json`, ESLint, and Prettier for code quality. Use `ts-node-dev` for development.
    *   **Success Metric**: Project can be started, and a basic `GET /health` endpoint returns a 200 status.

*   **Sub-Task 1.2**: Implement a scalable folder structure (`routes`, `services`, `controllers`, `middleware`).
    *   **Details**: Create placeholder files within each directory to establish the pattern. For example, a `trip.routes.ts`, `trip.controller.ts`, and `trip.service.ts`.
    *   **Success Metric**: The structure is committed to the repository and reviewed by the team.

*   **Sub-Task 1.3**: Create database migration scripts for core tables.
    *   **Details**: Use Supabase's migration tools to create the initial schema. All tables should include `created_at` and `updated_at` timestamps for audit and debugging purposes. Tables required:
        *   `profiles`: `id (UUID, FK to auth.users)`, `email`, `full_name`, `avatar_url`, `subscription_tier`.
        *   `trips`: `id (UUID)`, `user_id (FK to profiles)`, `title`, `description`, `start_date`, `end_date`, `is_public`, `share_token`, `view_count`.
        *   `activities`: `id (UUID)`, `trip_id (FK to trips)`, `title`, `start_time`, `location_name`, `latitude`, `longitude`.
        *   `affiliate_clicks`: `id`, `activity_id`, `user_id`, `provider`, `clicked_at`.
        *   `trip_shares`: `id`, `trip_id`, `shared_by_user_id`, `copied_by_user_id`.
    *   **Success Metric**: Migrations can be run successfully against a local and staging Supabase instance.

*   **Sub-Task 1.4**: Implement JWT authentication middleware.
    *   **Details**: Create middleware that verifies the `Authorization: Bearer <token>` header using `supabase.auth.getUser(jwt)`. It should attach the user object to the request for downstream controllers.
    *   **Success Metric**: Protected endpoints return a 401 error for invalid/missing tokens and a 200 for valid tokens.

*   **Sub-Task 1.5 (Low Priority)**: Create `Dockerfile` and `docker-compose.yml`.
    *   **Details**: Containerize the Node.js backend service to ensure development/production parity and simplify future developer onboarding.
    *   **Success Metric**: The backend can be started locally using `docker-compose up`.

*   **Sub-Task 1.6**: Implement Centralized Error-Handling Middleware.
    *   **Details**: Create a single middleware to catch all thrown errors, log them to the console (and later, Sentry), and return a standardized JSON error response to the client.
    *   **Success Metric**: All API errors return a consistent, structured JSON object.

### TASK 002: Core API Endpoint Implementation

**Owner**: Backend Engineer
**Goal**: Build the essential API endpoints for user and trip management.

*   **Sub-Task 2.1 (Auth)**:
    *   `POST /api/v1/auth/signup`
        *   **Request Body**: `{ email, password, fullName }`
        *   **Action**: Creates a user in `auth.users` via Supabase client. On success, creates a corresponding entry in the public `profiles` table.
        *   **Response**: `{ user, session }`
        *   **Success Metric**: Returns 201 on success. A new user is visible in the Supabase Auth dashboard and `profiles` table.
    *   `POST /api/v1/auth/login`
        *   **Request Body**: `{ email, password }`
        *   **Action**: Authenticates the user with Supabase.
        *   **Response**: `{ user, session }`
        *   **Success Metric**: Returns 200 on success with a valid JWT.

*   **Sub-Task 2.2 (Trips)**:
    *   `POST /api/v1/trips` (Protected)
        *   **Request Body**: `{ title, description, startDate, endDate }`
        *   **Action**: Creates a new trip record associated with the authenticated user.
        *   **Response**: The newly created trip object.
        *   **Success Metric**: Returns 201. The trip is correctly inserted into the `trips` table with the correct `user_id`.
    *   `GET /api/v1/trips` (Protected)
        *   **Action**: Fetches all trips for the authenticated user, sorted by `start_date` (newest first). Implements pagination with `limit` and `offset` query parameters.
        *   **Response**: `[{ id, title, destination, ... }]`
        *   **Success Metric**: Returns 200 with a paginated list of the user's trips.

### TASK 003: Frontend Integration & UI Activation

**Owner**: Frontend Engineer
**Goal**: Connect the frontend to the backend, replacing mock data and static pages with live functionality.

*   **Sub-Task 3.1**: Create a centralized API client service.
    *   **Details**: Use a library like `axios` or the native `fetch` API. The client should automatically attach the JWT from local storage to all authenticated requests.
    *   **Success Metric**: A single, reusable service handles all API calls.

*   **Sub-Task 3.2**: Implement the full user authentication flow.
    *   **Details**: Update the `/login` and `/signup` pages to use the API client. On successful login, store the JWT and user data in a global state management solution (like Zustand or React Context) and redirect to `/dashboard`.
    *   **Success Metric**: Users can sign up, log in, and the user's authentication state persists across the application.

*   **Sub-Task 3.3**: Activate the dashboard.
    *   **Details**: The `CreateTripModal` component should now call the `POST /api/v1/trips` endpoint. The main dashboard page should fetch and display the user's trips from `GET /api/v1/trips`.
    *   **Success Metric**: A user can create a trip, and it immediately appears on their dashboard grid.

---

## Phase 2: Core Feature Implementation (Days 16-30)

### TASK 004: AI Import Engine (MVP)

**Owner**: Backend Engineer
**Goal**: Enable users to import AI-generated text and have it automatically parsed into a structured trip.

*   **Sub-Task 4.1**: Implement a basic Regex-based parser.
    *   **Details**: Focus on simple patterns like `Day 1:`, `Flight AA123`, etc. **Crucially, when the parser fails, the exact input text that caused the failure must be logged.** This creates a dataset for future improvements.
    *   **Success Metric**: The parser correctly extracts data from at least 5 different sample texts. Failure cases are logged effectively.

*   **Sub-Task 4.2**: Integrate with OpenRouter as a fallback.
    *   **Details**: Use a cost-effective model. The prompt should be engineered to be robust, instructing the AI to return a structured JSON object based on the input text.
    *   **Success Metric**: The OpenRouter integration successfully parses complex text that the Regex parser fails on.

*   **Sub-Task 4.3**: Implement the `POST /api/v1/import/parse` endpoint.
    *   **Request Body**: `{ text: "..." }`
    *   **Action**: Runs the text through the two-tier parsing system. If successful, it creates a new trip and associated activities in the database for the user.
    *   **Response**: The newly created trip object with all its parsed activities.
    *   **Success Metric**: The endpoint returns a 201 and the database is populated correctly.

### TASK 005: Affiliate Revenue (MVP)

**Owner**: Backend Engineer
**Goal**: Integrate a single affiliate provider to generate revenue from flight bookings.

*   **Sub-Task 5.1**: Create a dedicated `AffiliateService` and a `GET /api/v1/flights/search` endpoint.
    *   **Details**: Create an isolated client/SDK for the Travelpayouts API to keep all provider-specific logic in one place. This service will be called by the search endpoint.
    *   **Request Query Params**: `?origin=JFK&destination=LAX&date=2024-12-25`
    *   **Action**: Calls the Travelpayouts API via the dedicated service.
    *   **Response**: A standardized list of flight options, including affiliate links.
    *   **Success Metric**: The endpoint returns valid flight data from the Travelpayouts API.

*   **Sub-Task 5.2**: Implement a basic click-tracking endpoint `POST /api/v1/affiliate/click`.
    *   **Request Body**: `{ activityId, provider, url }`
    *   **Action**: Logs the click event to the `affiliate_clicks` table.
    *   **Success Metric**: A new record is created in the database for each click.

### TASK 006: Viral Sharing & Growth Loop (MVP)

**Owner**: Full Team
**Goal**: Allow users to share their trips publicly, enabling viral growth.

*   **Sub-Task 6.1**: Implement the `POST /api/v1/trips/:id/share` endpoint.
    *   **Action**: Generates a unique, short `share_token`, sets `is_public` to true on the trip record.
    *   **Response**: `{ shareUrl: "https://travelviz.ai/p/TOKEN" }`
    *   **Success Metric**: The trip is made public and a shareable URL is returned.

*   **Sub-Task 6.2**: Build the public trip view page (`/p/[slug]`)
    *   **Details**: This page must be server-side rendered for SEO. It must include proper **Open Graph (e.g., `og:title`) and Twitter card meta tags** to ensure rich previews when shared on social media. It fetches trip data based on the `slug` (share token) and is read-only.
    *   **Success Metric**: The page displays trip details, is accessible without auth, and shows a rich preview when the URL is shared.

*   **Sub-Task 6.3**: Implement the "Copy This Trip" feature.
    *   **Action**: The button on the public page triggers a `POST /api/v1/trips/:id/copy` endpoint. This endpoint duplicates the trip and its activities for the currently authenticated user.
    *   **Success Metric**: A new, private trip is created in the user's account, identical to the shared one.

---

## Phase 3: Polish & Beta Launch (Days 31-45)

### TASK 007: Caching & Performance

**Owner**: Backend Lead
**Goal**: Ensure the application is fast and responsive for a good user experience.

*   **Sub-Task 7.1**: Implement Redis caching for external API calls.
    *   **Details**: Cache responses from the Travelpayouts API for at least 30 minutes to reduce redundant calls and improve speed.
    *   **Success Metric**: Subsequent identical flight searches return a cached response.

*   **Sub-Task 7.2**: Optimize frontend performance.
    *   **Details**: Use Next.js dynamic imports for large components. Optimize images using `next/image`. **Leverage Next.js Incremental Static Regeneration (ISR) for public trip pages** to cache the rendered HTML.
    *   **Success Metric**: Lighthouse performance score is above 90.

### TASK 008: Basic Analytics & Monitoring

**Owner**: Full Team
**Goal**: Understand user behavior and monitor application health.

*   **Sub-Task 8.1**: Integrate Plausible analytics.
    *   **Details**: Track key events: `signup`, `login`, `trip_created`, `trip_shared`, `booking_link_clicked`.
    *   **Success Metric**: Events are correctly appearing in the Plausible dashboard.

*   **Sub-Task 8.2**: Set up Sentry for error monitoring.
    *   **Details**: Configure for both the Next.js frontend and the Express backend.
    *   **Success Metric**: Errors thrown in the application are captured and reported in Sentry.

### TASK 009: Beta Launch

**Owner**: Whole Team
**Goal**: Onboard the first 50 users and gather critical feedback.

*   **Sub-Task 9.1**: Deploy the application to production.
    *   **Details**: Frontend on Vercel, Backend on Render.
    *   **Success Metric**: The application is live and accessible at `travelviz.ai`.

*   **Sub-Task 9.2**: Send out invites to the waitlist.
    *   **Details**: Email the top 100 users on the waitlist with a link to the application.
    *   **Success Metric**: At least 50 users sign up and create their first trip.

---
### What We're NOT Building for MVP (From PRD)
- **Dual-Provider Flight Coverage**: Sticking to one provider simplifies the initial build.
- **Full Offline-First Mobile App**: A responsive web app is the priority.
- **Advanced AI Chat/Suggestions**: The core value is the one-time import, not an ongoing conversation.
- **Real-time Collaborative Editing**: Sharing and copying is sufficient for the MVP growth loop.
- **Complex Subscription Tiers**: The focus is on affiliate revenue first. We can offer the service for free initially to drive adoption.
