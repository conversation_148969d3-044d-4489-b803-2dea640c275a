"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { 
  MapPin, 
  Calendar, 
  Clock, 
  DollarSign, 
  Eye, 
  Share2, 
  Copy, 
  Download,
  Heart,
  Star,
  User,
  ArrowRight,
  ExternalLink
} from 'lucide-react';
import { motion } from 'framer-motion';

// Mock public trip data
const mockPublicTrip = {
  id: 'tokyo-kyoto-adventure',
  slug: 'tokyo-kyoto-adventure',
  title: '7 Days in Tokyo & Kyoto',
  description: 'A perfect blend of modern Tokyo and traditional Kyoto, featuring the best temples, restaurants, and cultural experiences Japan has to offer.',
  destination: 'Japan',
  startDate: '2024-04-15',
  endDate: '2024-04-22',
  duration: 7,
  viewCount: 1247,
  likeCount: 89,
  author: {
    name: '<PERSON>',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=100',
    verified: true,
    tripCount: 12
  },
  tags: ['Culture', 'Food', 'Temples', 'First-time'],
  estimatedCost: 1200,
  difficulty: 'Easy',
  bestTime: 'Spring (March-May)',
  days: [
    {
      day: 1,
      date: '2024-04-15',
      theme: 'Tokyo Arrival & Shibuya',
      items: [
        {
          id: 1,
          time: '14:00',
          title: 'Arrive at Haneda Airport',
          description: 'International flight arrival, collect luggage and get JR Pass',
          location: { name: 'Haneda Airport', address: 'Tokyo, Japan' },
          category: 'transport',
          duration: 60,
          cost: 0,
          tips: 'Get your JR Pass activated here to save time later'
        },
        {
          id: 2,
          time: '16:00',
          title: 'Check into Hotel in Shibuya',
          description: 'Modern hotel in the heart of Shibuya district with great city views',
          location: { name: 'Shibuya District', address: 'Shibuya, Tokyo, Japan' },
          category: 'accommodation',
          duration: 30,
          cost: 120,
          tips: 'Ask for a room facing the crossing for the best views'
        },
        {
          id: 3,
          time: '18:00',
          title: 'Shibuya Crossing Experience',
          description: 'Experience the world\'s busiest pedestrian crossing and visit the observation deck',
          location: { name: 'Shibuya Crossing', address: 'Shibuya, Tokyo, Japan' },
          category: 'activity',
          duration: 90,
          cost: 0,
          tips: 'Best photo spots are from Starbucks or the Sky observation deck'
        },
        {
          id: 4,
          time: '20:00',
          title: 'Dinner at Traditional Izakaya',
          description: 'Authentic Japanese pub experience with local sake and small plates',
          location: { name: 'Torikizoku Shibuya', address: 'Shibuya, Tokyo, Japan' },
          category: 'restaurant',
          duration: 120,
          cost: 45,
          tips: 'Try the yakitori and don\'t be afraid to point at the menu'
        }
      ]
    },
    {
      day: 2,
      date: '2024-04-16',
      theme: 'Traditional Tokyo',
      items: [
        {
          id: 5,
          time: '09:00',
          title: 'Senso-ji Temple',
          description: 'Tokyo\'s oldest temple in historic Asakusa district',
          location: { name: 'Senso-ji Temple', address: 'Asakusa, Tokyo, Japan' },
          category: 'activity',
          duration: 120,
          cost: 5,
          tips: 'Visit early to avoid crowds and try fortune telling'
        },
        {
          id: 6,
          time: '12:00',
          title: 'Sushi Lunch at Tsukiji',
          description: 'Fresh sushi at the famous fish market area',
          location: { name: 'Tsukiji Outer Market', address: 'Tsukiji, Tokyo, Japan' },
          category: 'restaurant',
          duration: 60,
          cost: 40,
          tips: 'Try the tuna sashimi and tamago - it\'s incredibly fresh'
        }
      ]
    }
  ]
};

export default function PublicTripPage({ params }: { params: { slug: string } }) {
  const [trip] = useState(mockPublicTrip);
  const [selectedDay, setSelectedDay] = useState(1);
  const [isLiked, setIsLiked] = useState(false);
  const [showCopyModal, setShowCopyModal] = useState(false);

  useEffect(() => {
    // Increment view count (would be done server-side)
    console.log('Incrementing view count for trip:', params.slug);
  }, [params.slug]);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: trip.title,
          text: trip.description,
          url: window.location.href,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const handleCopyTrip = () => {
    // Redirect to signup with source tracking
    window.location.href = `/signup?source=copy_trip&trip=${trip.slug}`;
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'activity': return 'bg-blue-500';
      case 'restaurant': return 'bg-red-500';
      case 'accommodation': return 'bg-green-500';
      case 'transport': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  const calculateDayStats = (day: any) => {
    const totalCost = day.items.reduce((sum: number, item: any) => sum + item.cost, 0);
    const totalDuration = day.items.reduce((sum: number, item: any) => sum + item.duration, 0);
    return { totalCost, totalDuration: Math.round(totalDuration / 60 * 10) / 10 };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-orange-500 to-pink-500 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                {trip.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="bg-white/20 text-white border-white/30">
                    {tag}
                  </Badge>
                ))}
              </div>
              
              <h1 className="text-4xl md:text-5xl font-bold mb-4">{trip.title}</h1>
              <p className="text-xl text-white/90 mb-6">{trip.description}</p>
              
              <div className="flex items-center space-x-6 text-white/80">
                <div className="flex items-center space-x-1">
                  <Calendar className="h-4 w-4" />
                  <span>{trip.duration} days</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>{trip.destination}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Eye className="h-4 w-4" />
                  <span>{trip.viewCount.toLocaleString()} views</span>
                </div>
              </div>
            </div>

            <div className="lg:col-span-1">
              <Card className="p-6 bg-white/10 backdrop-blur-sm border-white/20">
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center space-x-4">
                    <img
                      src={trip.author.avatar}
                      alt={trip.author.name}
                      className="w-12 h-12 rounded-full border-2 border-white"
                    />
                    <div className="text-left">
                      <div className="flex items-center space-x-1">
                        <span className="font-semibold">{trip.author.name}</span>
                        {trip.author.verified && <Star className="h-4 w-4 text-yellow-300" />}
                      </div>
                      <p className="text-sm text-white/70">{trip.author.tripCount} trips shared</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">${trip.estimatedCost}</div>
                      <div className="text-sm text-white/70">Est. Budget</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{trip.difficulty}</div>
                      <div className="text-sm text-white/70">Difficulty</div>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Button 
                      onClick={handleCopyTrip}
                      className="w-full bg-white text-orange-500 hover:bg-gray-100"
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Copy to My Account
                    </Button>
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 border-white/30 text-white hover:bg-white/10"
                        onClick={() => setIsLiked(!isLiked)}
                      >
                        <Heart className={`h-4 w-4 mr-1 ${isLiked ? 'fill-current' : ''}`} />
                        {trip.likeCount + (isLiked ? 1 : 0)}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex-1 border-white/30 text-white hover:bg-white/10"
                        onClick={handleShare}
                      >
                        <Share2 className="h-4 w-4 mr-1" />
                        Share
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Map Section */}
          <div className="lg:col-span-2">
            <Card className="p-6 mb-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Interactive Map</h2>
              
              {/* Map Placeholder */}
              <div className="w-full h-96 bg-gradient-to-br from-blue-100 to-green-100 rounded-lg flex items-center justify-center relative overflow-hidden">
                {/* Mock map markers */}
                <div className="absolute inset-0">
                  {trip.days[selectedDay - 1]?.items.map((item, index) => (
                    <div
                      key={item.id}
                      className={`absolute w-8 h-8 ${getCategoryColor(item.category)} rounded-full flex items-center justify-center text-white text-xs font-bold shadow-lg cursor-pointer hover:scale-110 transition-transform`}
                      style={{
                        top: `${20 + index * 15}%`,
                        left: `${30 + index * 10}%`
                      }}
                      title={item.title}
                    >
                      {index + 1}
                    </div>
                  ))}
                </div>
                <div className="text-center text-gray-600 z-10">
                  <MapPin className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium">Interactive Map View</p>
                  <p className="text-sm">Explore all locations for Day {selectedDay}</p>
                </div>
              </div>
            </Card>

            {/* Trip Tips */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Travel Tips</h3>
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <p className="text-gray-700">Best time to visit: {trip.bestTime}</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <p className="text-gray-700">Get a JR Pass for unlimited train travel - it pays for itself in 2-3 trips</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <p className="text-gray-700">Download Google Translate with camera feature for menus and signs</p>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                  <p className="text-gray-700">Cash is still king in Japan - withdraw from 7-Eleven ATMs</p>
                </div>
              </div>
            </Card>
          </div>

          {/* Timeline Section */}
          <div className="space-y-6">
            {/* Day Selector */}
            <Card className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Daily Itinerary</h3>
              
              <div className="flex space-x-2 mb-4">
                {trip.days.map((day) => (
                  <button
                    key={day.day}
                    onClick={() => setSelectedDay(day.day)}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      selectedDay === day.day
                        ? 'bg-orange-500 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Day {day.day}
                  </button>
                ))}
              </div>

              {trip.days.map((day) => {
                if (day.day !== selectedDay) return null;
                
                const stats = calculateDayStats(day);
                
                return (
                  <div key={day.day} className="space-y-4">
                    {/* Day Header */}
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold text-gray-900">{day.theme}</h4>
                      <p className="text-sm text-gray-600 mb-3">{day.date}</p>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <div className="font-medium text-gray-900">${stats.totalCost}</div>
                          <div className="text-gray-600">Total Cost</div>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{stats.totalDuration}h</div>
                          <div className="text-gray-600">Duration</div>
                        </div>
                      </div>
                    </div>

                    {/* Activities */}
                    <div className="space-y-3">
                      {day.items.map((item, index) => (
                        <motion.div
                          key={item.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="bg-white border border-gray-200 rounded-lg p-4"
                        >
                          <div className="flex items-start space-x-3">
                            <div className={`w-8 h-8 ${getCategoryColor(item.category)} rounded-full flex items-center justify-center text-white text-xs font-bold flex-shrink-0`}>
                              {index + 1}
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <h5 className="text-sm font-medium text-gray-900 mb-1">
                                {item.title}
                              </h5>
                              
                              <div className="flex items-center space-x-2 text-xs text-gray-500 mb-2">
                                <Clock className="h-3 w-3" />
                                <span>{item.time}</span>
                                <span>•</span>
                                <span>{item.duration} min</span>
                                <span>•</span>
                                <span>${item.cost}</span>
                              </div>
                              
                              <p className="text-xs text-gray-600 mb-2">{item.description}</p>
                              <p className="text-xs text-gray-500 mb-2">{item.location.name}</p>
                              
                              {item.tips && (
                                <div className="bg-orange-50 border border-orange-200 rounded p-2 mt-2">
                                  <p className="text-xs text-orange-800">
                                    <strong>Tip:</strong> {item.tips}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </Card>

            {/* CTA Card */}
            <Card className="p-6 bg-gradient-to-r from-orange-500 to-pink-500 text-white">
              <div className="text-center space-y-4">
                <h3 className="text-lg font-semibold">Love this itinerary?</h3>
                <p className="text-white/90 text-sm">
                  Copy it to your account and customize it for your own adventure
                </p>
                <Button 
                  onClick={handleCopyTrip}
                  className="w-full bg-white text-orange-500 hover:bg-gray-100"
                >
                  <User className="h-4 w-4 mr-2" />
                  Sign Up Free to Copy
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
                <p className="text-xs text-white/70">
                  No credit card required • 3 free trips included
                </p>
              </div>
            </Card>

            {/* Export Options */}
            <Card className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Export Options</h3>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Download className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open in Google Maps
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer CTA */}
      <div className="bg-gray-900 text-white py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Ready to Plan Your Own Adventure?</h2>
          <p className="text-gray-300 mb-6">
            Create beautiful visual itineraries like this one with our AI-powered travel planner
          </p>
          <Button 
            onClick={handleCopyTrip}
            size="lg" 
            className="bg-orange-500 hover:bg-orange-600"
          >
            Start Planning Free
            <ArrowRight className="h-5 w-5 ml-2" />
          </Button>
          <p className="text-sm text-gray-400 mt-4">
            Created with ❤️ using TravelViz.com
          </p>
        </div>
      </div>
    </div>
  );
}