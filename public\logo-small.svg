<svg width="40" height="40" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="100" cy="100" r="85" fill="url(#backgroundGradient)"/>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF8C42;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4ECDC4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45B7AA;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#7CB342;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8BC34A;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Top Orange Section -->
  <path d="M 30 100 Q 100 80 170 100 L 170 40 Q 100 20 30 40 Z" fill="#FF8C42"/>
  
  <!-- Bottom Teal Section -->
  <path d="M 30 100 Q 100 120 170 100 L 170 160 Q 100 180 30 160 Z" fill="#4ECDC4"/>
  
  <!-- Green Wave at Bottom -->
  <path d="M 30 150 Q 100 140 170 150 L 170 170 Q 100 160 30 170 Z" fill="url(#waveGradient)"/>
  
  <!-- Simplified Icons for Small Size -->
  <rect x="45" y="50" width="20" height="25" rx="2" fill="white"/>
  <path d="M 50 60 L 52 62 L 57 57" stroke="#FF8C42" stroke-width="2" fill="none"/>
  <text x="135" y="75" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white">$</text>
  <circle cx="50" cy="130" r="5" fill="white"/>
  <circle cx="62" cy="130" r="5" fill="white"/>
  <rect x="135" y="120" width="15" height="18" rx="2" fill="white"/>
  
  <!-- Airplane -->
  <g transform="translate(100,100) rotate(25)">
    <path d="M -15 -4 L -10 -6 L 12 -2 L 15 0 L 12 2 L -10 6 L -15 4 Z" fill="#2C3E50"/>
    <path d="M -8 -6 L -4 -12 L 0 -6 Z" fill="#2C3E50"/>
    <path d="M -8 6 L -4 12 L 0 6 Z" fill="#2C3E50"/>
  </g>
</svg> 