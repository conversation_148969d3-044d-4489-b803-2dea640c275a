import express, { Application } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { createSuccessResponse, createErrorResponse, HTTP_STATUS } from '@travelviz/shared';

// Load environment variables
dotenv.config();

const app: Application = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true,
}));
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json(createSuccessResponse({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'travelviz-hub',
  }));
});

// API routes
app.get('/api/test', (req, res) => {
  res.json(createSuccessResponse({
    message: 'TravelViz Hub API is working!',
    timestamp: new Date().toISOString(),
  }));
});

// 404 handler
app.use('*', (req, res) => {
  res.status(HTTP_STATUS.NOT_FOUND).json(
    createErrorResponse('Not Found', `Route ${req.originalUrl} not found`)
  );
});

// Error handler
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  
  const status = err.status || HTTP_STATUS.INTERNAL_SERVER_ERROR;
  const message = err.message || 'Internal Server Error';
  
  res.status(status).json(
    createErrorResponse(message, 'An unexpected error occurred')
  );
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 TravelViz Hub API server running on port ${PORT}`);
  console.log(`📍 Health check: http://localhost:${PORT}/health`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
});

export default app; 