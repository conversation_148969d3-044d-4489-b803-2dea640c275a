{"name": "@travelviz/hub", "version": "1.0.0", "private": true, "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@travelviz/shared": "workspace:*", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^20.14.0", "eslint": "^8.57.0", "jest": "^29.7.0", "tsx": "^4.19.1", "typescript": "^5.4.0"}}