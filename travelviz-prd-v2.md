
# Product Requirements Document (PRD)
## TravelViz - AI-Powered Travel Companion
### Version 2.1 | December 2024

---

## Executive Summary

**Product Name:** TravelViz  
**Domain:** travelviz.ai  
**Vision:** Transform chaotic AI-generated travel plans into beautiful, shareable, and actionable itineraries in 30 seconds  
**Mission:** Eliminate the 15+ tab problem for modern travelers by creating the ultimate AI-to-itinerary bridge  
**Target Launch:** Q2 2025  
**Success Metric:** 10,000 active users within 6 months  

### Key Differentiators
- **Universal AI Import**: Purpose-built for parsing ChatGPT/Claude/Gemini conversations with 95%+ accuracy
- **Visual-First Design**: Interactive timeline and Mapbox-powered maps with custom photo markers
- **Viral Sharing DNA**: Every trip is a potential template for others
- **Offline-First Architecture**: Full functionality without internet using Mapbox offline maps
- **Dual-Provider Flight Coverage**: Comprehensive US domestic (Duffel) and international (Travelpayouts) coverage

---

## Product Context & Problem Statement

### The Problem Space

Modern travelers increasingly rely on AI assistants (<PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>) for trip planning, creating a new behavioral pattern:
1. Users have 5-10 back-and-forth conversations with AI to refine their trip
2. The output lives in chat history, difficult to reference or act upon
3. Actual booking happens across 15+ browser tabs
4. Sharing plans requires copy-pasting walls of text
5. No price tracking or offline access to AI-generated plans

### Market Opportunity

- **Market Size**: 1.5B international travelers annually
- **AI Assistant Users**: 200M+ people using ChatGPT/Claude monthly
- **Target Segment**: 25-45 year olds planning 2-5 trips annually
- **Revenue Potential**: $50B travel planning market

### User Research Insights

From 500 surveyed AI-assisted travel planners:
- 87% lose their AI conversation history within 30 days
- 76% recreate the same trip planning conversations multiple times
- 82% struggle to share AI-generated plans with travel companions
- 91% manually copy information between AI chat and booking sites
- 68% would pay for a tool that bridges AI planning to real bookings

---

## User Personas & Journey Maps

### Primary Personas

#### 1. "The AI Native Planner" - Sarah Chen, 32
**Demographics:**
- Product Manager at tech company
- San Francisco, CA
- Income: $120k/year
- Travel: 4-5 trips annually (2 international)

**Behaviors:**
- Uses Claude/ChatGPT for everything
- Plans trips 2-3 months in advance
- Books flights/hotels separately for points
- Shares detailed itineraries with friends

**Pain Points:**
- Loses ChatGPT conversations after 30 days
- Manually recreates same prompts for similar trips
- Copy-pastes between 20+ tabs when booking
- Friends complain about wall-of-text itineraries

**Goals:**
- Seamless AI conversation → bookable itinerary
- Easy sharing with travel group
- Price tracking without manual checking
- Offline access during trips

**Quote:** "I spent 3 hours perfecting my Japan itinerary with Claude, then lost it all when my browser crashed."

#### 2. "The Group Trip Coordinator" - Michael Rodriguez, 38
**Demographics:**
- Marketing Director
- Austin, TX
- Income: $95k/year
- Travel: 3 annual trips (1 solo, 2 group)

**Behaviors:**
- Organizes annual friend/family trips
- Uses AI to generate options, then refines
- Juggles multiple people's preferences
- Needs buy-in from 4-8 people

**Pain Points:**
- Difficult to share AI conversations with group
- No way to collaborate on AI-generated plans
- Version control nightmare with group edits
- Tracking who has booked what

**Goals:**
- One link to share entire trip plan
- See who has viewed/approved plans
- Collaborative editing capabilities
- Group booking coordination

**Quote:** "Getting 6 friends to agree on a trip is hard enough without juggling 50 email threads."

#### 3. "The Budget Optimizer" - Emma Thompson, 27
**Demographics:**
- Graduate student
- Boston, MA
- Income: $35k/year
- Travel: 2 trips annually (budget-focused)

**Behaviors:**
- Price-sensitive, books 3-6 months ahead
- Uses AI to find budget alternatives
- Tracks prices obsessively
- Optimizes every dollar spent

**Pain Points:**
- Manually checking prices daily
- Missing price drops while busy
- AI suggestions often over budget
- No easy way to track total trip cost

**Goals:**
- Automatic price drop alerts
- Budget tracking within itinerary
- Find deals on AI-suggested places
- Share costs with travel partners

**Quote:** "I asked Claude for a budget Europe trip, but still had to check 15 sites daily for deals."

### User Journey Maps

#### Journey 1: AI Conversation to Booked Trip

**Stages:**
1. **Discovery** (2-3 hours)
   - User has multiple ChatGPT/Claude conversations
   - Refines preferences, dates, budget
   - Gets final itinerary in chat

2. **Capture** (Current: 30 mins of copy-paste)
   - **With TravelViz**: Paste conversation URL/text → 5 seconds
   - Auto-parsing extracts all details
   - Visual preview appears instantly

3. **Organize** (Current: 1-2 hours)
   - **With TravelViz**: Drag-drop timeline adjustments
   - Map view shows logical routing
   - Auto-suggestions for timing conflicts

4. **Share** (Current: Messy email chains)
   - **With TravelViz**: One beautiful link
   - Recipients see interactive timeline
   - "Copy this trip" for easy duplication

5. **Book** (Current: 15+ tabs)
   - **With TravelViz**: Direct booking links
   - Price tracking activated
   - Best prices highlighted (Duffel for US, Travelpayouts for intl)
   - Confirmations auto-imported

6. **Travel** (Current: Screenshots/PDFs)
   - **With TravelViz**: Full offline access with Mapbox
   - Real-time updates if online
   - One tap to reservation details

#### Journey 2: Viral Trip Sharing

**Stages:**
1. **Creation**: User builds amazing Barcelona trip
2. **Sharing**: Posts link in Reddit travel forum
3. **Discovery**: 50 people click through
4. **Copying**: 15 people copy and customize
5. **Network Effect**: Those 15 share their versions
6. **Attribution**: Original creator sees impact

---

## System Architecture: Hub-Centric Design

### Architecture Philosophy

TravelViz implements a **centralized hub architecture** where all system components communicate exclusively through a single, intelligent hub service. This design ensures consistency, simplifies maintenance, and enables rapid feature development.

### High-Level Architecture

```
┌─────────────────────────────────────────────────┐
│                   CLIENTS                       │
├─────────────────┬───────────────────────────────┤
│   Next.js Web   │   React Native Mobile         │
└────────┬────────┴────────────┬──────────────────┘
         │                     │
         ↓                     ↓
┌─────────────────────────────────────────────────┐
│           THE HUB (Node.js/Express)             │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │          API Gateway Layer               │   │
│  │  • JWT Authentication                    │   │
│  │  • Rate Limiting                         │   │
│  │  • Request Validation                    │   │
│  │  • Error Handling                        │   │
│  └─────────────────────────────────────────┘   │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │         Business Logic Core              │   │
│  │  • Trip Management Service               │   │
│  │  • AI Parsing Engine                     │   │
│  │  • Sharing System                        │   │
│  │  • Price Tracking Engine                 │   │
│  │  • Affiliate Link Generator              │   │
│  └─────────────────────────────────────────┘   │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │         Service Layer                    │   │
│  │  • Database Queries (Prisma)             │   │
│  │  • Cache Management (Redis)              │   │
│  │  • External API Orchestration            │   │
│  │  • Background Job Queue (Bull)           │   │
│  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────┘
         │                     │
         ↓                     ↓
┌─────────────────┐   ┌───────────────────────┐
│   PostgreSQL    │   │   External APIs       │
│   (Supabase)    │   │   • OpenRouter AI     │
│                 │   │   • Travelpayouts     │
│                 │   │   • Duffel API       │
│                 │   │   • Hotels.com        │
│                 │   │   • Mapbox            │
└─────────────────┘   └───────────────────────┘
```

### Hub Communication Patterns

**1. Frontend → Hub → Database**
```javascript
// Frontend never talks directly to database
const getTrip = async (tripId) => {
  return await fetch(`${HUB_URL}/api/trips/${tripId}`, {
    headers: { Authorization: `Bearer ${token}` }
  });
};
```

**2. Hub → External APIs → Smart Caching**
```javascript
// Hub manages all external API calls with intelligent caching
const searchFlights = async (params) => {
  // Check cache first
  const cached = await redis.get(getCacheKey(params));
  if (cached && !isExpired(cached)) return cached;
  
  // Determine provider based on route
  const isUSdomestic = isUSRoute(params.origin, params.destination);
  
  // Call appropriate provider
  const results = isUSdomestic
    ? await duffel.searchFlights(params)
    : await travelpayouts.search(params);
  
  // Apply $10 rule and cache results
  const processed = applyTenDollarRule(results);
  await redis.setex(getCacheKey(params), 1800, processed); // 30 min cache
  return processed;
};
```

### Why Hub Architecture for TravelViz

1. **Single Source of Truth**: All business logic lives in one place
2. **Easier Debugging**: One log stream to monitor
3. **Consistent Security**: Auth/validation in one layer
4. **Rapid Development**: No coordination between services
5. **Cost Effective**: Single deployment, easier DevOps

---

## Revenue Model: Dual Revenue Streams

### Core Philosophy

Users come to TravelViz for the exceptional trip planning experience. Our revenue model combines invisible affiliate commissions with optional premium subscriptions.

### Revenue Streams

#### 1. Affiliate Commissions (Primary - 70% of revenue)

**Dual-Provider Flight Strategy**

**Duffel API (US Domestic Focus)**
- **Coverage**: Major US carriers including alternatives
- **Pricing**: $3 + 1% markup per booking
- **Average Commission**: $6.50 per domestic booking
- **Why**: Modern API, excellent US coverage, no setup fees

**Travelpayouts (International Focus)**
- **Coverage**: 700+ airlines globally
- **Commission**: 1.6% average
- **Average Commission**: $12.80 per international booking
- **Why**: Free API, excellent international coverage

**Hotel Partners**
1. **Hotels.com/Expedia**
   - 3.2-6% commission
   - Free API tier
   - 1M+ properties

2. **Agoda**
   - 4-7% tiered commission
   - Strong Asia-Pacific coverage
   - Volume-based growth

3. **Booking.com** (When available)
   - 4% flat rate
   - Largest inventory
   - Highest conversions

#### 2. Premium Subscriptions (Secondary - 30% of revenue)

**TravelViz Free**
- 3 active trips
- 5 price alerts per trip
- Basic sharing features
- 7-day offline access
- Community templates

**TravelViz Pro ($4.99/month)**
- Unlimited trips
- Unlimited price alerts
- Advanced AI parsing
- Priority support
- No watermarks
- 30-day offline access
- Early access features

**TravelViz Teams ($7.99/user/month)**
- Everything in Pro
- Collaborative editing
- Team templates
- Expense tracking
- API access
- Custom branding
- Admin dashboard

### The $10 Rule Implementation

```javascript
// Hub implements smart price comparison
function selectBookingLink(allPrices) {
  const lowestPrice = Math.min(...allPrices.map(p => p.price));
  
  // Find affiliate links within $10 of lowest
  const eligibleLinks = allPrices.filter(option => {
    const diff = option.price - lowestPrice;
    return diff <= 10 && option.hasAffiliateLink;
  });
  
  // Prioritize by provider quality
  if (eligibleLinks.length > 0) {
    return eligibleLinks.sort((a, b) => {
      if (isUSdomestic && a.provider === 'duffel') return -1;
      if (!isUSdomestic && a.provider === 'travelpayouts') return -1;
      return b.commission - a.commission;
    })[0];
  }
  
  return allPrices.find(p => p.price === lowestPrice);
}
```

---

## Feature Requirements

### P0 Features (Launch Critical)

#### 1. Universal AI Import Engine

**Overview:** Industry-leading AI conversation parser with Hub-based processing

**Detailed Requirements:**

```typescript
interface ImportCapabilities {
  sources: [
    'Claude conversation URL',
    'ChatGPT shared link',
    'Gemini conversation',
    'Copy-paste text from any AI',
    'Email forwards',
    'Raw text itinerary'
  ];
  
  parseTargets: {
    dates: 'All date formats, relative dates, date ranges';
    locations: 'Cities, POIs, addresses, regions';
    activities: 'Museums, restaurants, tours, transport';
    accommodations: 'Hotels, Airbnbs, hostels';
    flights: 'Airlines, flight numbers, times';
    preferences: 'Budget, pace, interests';
  };
  
  accuracy: {
    target: 95%;
    measurement: 'Correct field extraction rate';
  };
}
```

**Hub Processing Flow:**
1. Frontend sends raw text to Hub
2. Hub applies 3-tier parsing strategy:
   - Regex patterns (free, handles 60% of imports)
   - Local LLM (cheap, handles 30% of imports)
   - OpenRouter API (Claude/GPT-4/Gemini models, handles 10% complex cases)
3. Hub returns structured trip data
4. Frontend displays for user confirmation

**Cost Optimization:**
```javascript
// Hub manages parsing costs intelligently
async function parseImport(content) {
  // Try regex first (FREE)
  const regexResult = await regexParser.parse(content);
  if (regexResult.confidence > 0.8) return regexResult;
  
  // Try local model ($0.02)
  const localResult = await localLLM.parse(content);
  if (localResult.confidence > 0.8) return localResult;
  
  // Fall back to OpenRouter API ($0.10)
  return await openRouterAPI.parse(content, {
    models: ['claude-3-opus', 'gpt-4', 'gemini-pro'],
    maxCost: 0.10
  });
}
```

**UI Flow:**
1. Landing page with prominent "Paste your AI conversation" box
2. Real-time parsing animation showing extraction progress
3. Preview of parsed itinerary with confidence indicators
4. One-click confirmation or manual adjustment options

**Technical Implementation:**
- Primary: OpenRouter API for complex parsing
- Secondary: Regex patterns for standard formats
- Tertiary: Local LLM for simple extractions
- Cost optimization: 60% regex, 30% local, 10% API

#### 2. Visual Timeline & Map System (Powered by Mapbox)

**Overview:** Dual-view system showing temporal and spatial trip organization

**Why Mapbox:**
- **Custom photo markers**: Full control over photo display
- **Offline maps**: Critical for international travelers
- **Cost effective**: $500/month vs Google's $770+
- **Developer friendly**: Award-winning documentation

**Timeline View Specifications:**

```typescript
interface TimelineView {
  layout: {
    orientation: 'vertical';
    daysAsSwimLanes: true;
    timeScale: 'proportional'; // Morning/afternoon/evening
    style: 'modern-minimal';
  };
  
  interactions: {
    dragDrop: {
      between: 'days and time slots';
      animation: 'smooth bounce - 300ms';
      constraints: 'respect operating hours';
    };
    
    expand: {
      trigger: 'click on activity';
      animation: 'slide down - 200ms';
      content: 'full details, notes, bookings';
    };
    
    quickEdit: {
      trigger: 'double click';
      fields: ['time', 'duration', 'notes'];
      save: 'auto-save with optimistic UI';
    };
  };
  
  visualCues: {
    transportation: 'dotted line connectors';
    walkingTime: 'subtle time indicators';
    conflicts: 'red highlight with suggestions';
    freeTime: 'striped zones';
  };
}
```

**Mapbox Map View Specifications:**

```typescript
interface MapboxView {
  provider: 'Mapbox GL JS v3';
  
  style: {
    base: 'mapbox://styles/mapbox/light-v11';
    customization: 'Brand colors and travel-optimized POIs';
  };
  
  markers: {
    type: 'HTML markers with custom photos';
    clustering: 'Supercluster library for performance';
    colors: 'by day (gradient progression)';
    size: 'by importance/duration';
    animation: 'pulse on hover';
  };
  
  offline: {
    regions: 'Download by trip bounds';
    storage: 'Up to 6GB per device';
    sync: 'Automatic when on WiFi';
  };
  
  routing: {
    display: 'animated path drawing';
    modes: ['walking', 'transit', 'driving'];
    timing: 'show duration badges';
    optimization: 'suggest better routes';
  };
  
  interactions: {
    click: 'fly to location + show details';
    hover: 'highlight in timeline';
    zoom: 'cluster by neighborhood at low zoom';
    draw: 'add custom walking routes';
  };
}
```

**Synchronization Behaviors:**
- Select in timeline → Map pans and highlights
- Click on map → Timeline scrolls and highlights
- Drag in timeline → Map updates route in real-time
- Zoom on map → Timeline filters to visible area

#### 3. Viral Sharing Engine

**Overview:** One-click sharing with built-in viral mechanics

**Share Link Behavior:**
```typescript
interface ShareableTrip {
  url: {
    format: 'travelviz.ai/t/[unique-8-char]';
    preview: 'Rich OpenGraph cards';
    customization: 'Custom OG image per trip';
  };
  
  permissions: {
    public: 'Anyone can view and copy';
    unlisted: 'Only with link';
    private: 'Requires authentication';
  };
  
  copyMechanics: {
    button: 'Prominent "Copy This Trip" CTA';
    requireAuth: false; // Can copy without account
    prompt: 'After copy - soft signup prompt';
    modifications: 'Track drift from original';
  };
  
  viralTracking: {
    metrics: ['views', 'copies', 'modifications'];
    attribution: 'Link back to original';
    leaderboard: 'Most copied trips';
    badges: 'Viral trip creator';
  };
}
```

**Social Preview Requirements:**
- Dynamic image generation showing map + highlights
- Trip title, duration, and key stops
- "Created with TravelViz" watermark
- Creator attribution (optional)

#### 4. Intelligent Price Tracking

**Overview:** Automated price monitoring with seamless booking

**Tracking Specifications:**
```typescript
interface PriceTracking {
  supported: {
    flights: {
      domestic: 'Duffel API';
      international: 'Travelpayouts';
    };
    hotels: ['Booking.com', 'Hotels.com', 'direct'];
    activities: ['GetYourGuide', 'Viator'];
  };
  
  monitoring: {
    frequency: 'Every 6 hours';
    history: '90-day retention';
    predictions: 'ML-based price forecasting';
  };
  
  alerts: {
    triggers: [
      'Drop > 10% or $50',
      'Predicted lowest point',
      'Price increasing trend',
      'Last-minute deals'
    ];
    
    delivery: {
      email: 'Daily digest option';
      push: 'Immediate for big drops';
      inApp: 'Bell icon with number';
    };
  };
  
  visualization: {
    graph: 'Sparkline with annotations';
    comparison: 'vs. initial price';
    recommendation: 'Buy/wait indicator';
  };
}
```

#### 5. Offline-First Mobile Experience

**Overview:** Full-featured mobile app with complete offline capability using Mapbox

**Offline Specifications:**
```typescript
interface OfflineCapability {
  autoSync: {
    trigger: 'Open trip = download all';
    includes: ['itinerary', 'maps', 'documents'];
    mapDetail: 'Street level for all trip areas';
    updateCheck: 'On app launch if online';
  };
  
  localStorage: {
    database: 'SQLite for complex queries';
    documents: 'Encrypted file storage';
    images: 'Progressive loading + cache';
    mapTiles: 'Mapbox vector tiles for zoom 10-17';
  };
  
  offlineFeatures: [
    'Full itinerary access',
    'Map navigation with GPS',
    'Add/edit notes',
    'Photo attachment',
    'Bookmark places'
  ];
  
  syncStrategy: {
    conflictResolution: 'Last write wins + versioning';
    queue: 'Operations queued when offline';
    merge: 'Smart merge for collaborative trips';
  };
}
```

### P1 Features (Post-Launch)

#### 1. AI-Powered Suggestions
- "Ask AI" button for real-time trip help
- Context-aware recommendations
- Natural language trip modifications

#### 2. Collaborative Planning
- Real-time multi-user editing
- Comments and discussions
- Vote on activities
- Shared expense tracking

#### 3. Smart Booking Assistant
- Auto-fill booking forms
- Group booking coordination
- Loyalty program optimization
- Payment splitting

---

## Technical Architecture Details

### Hub Technology Stack

```javascript
{
  "hub": {
    "framework": "Express.js",
    "language": "TypeScript",
    "database": "Prisma ORM → PostgreSQL",
    "cache": "Redis (Upstash free tier)",
    "queue": "Bull (background jobs)",
    "auth": "JWT + Supabase Auth",
    "ai": "OpenRouter API (Claude/GPT-4/Gemini)",
    "monitoring": "Sentry"
  },
  
  "frontend": {
    "web": "Next.js 14 + TypeScript + Tailwind",
    "mobile": "React Native + Expo",
    "state": "Zustand",
    "maps": "Mapbox GL JS v3"
  },
  
  "apis": {
    "flights": {
      "domestic": "Duffel API",
      "international": "Travelpayouts"
    },
    "hotels": "Hotels.com via Expedia",
    "maps": "Mapbox",
    "ai": "OpenRouter"
  },
  
  "deployment": {
    "hub": "Render ($25/mo)",
    "web": "Vercel ($20/mo)",
    "database": "Supabase ($25/mo)",
    "worker": "Render Worker ($7/mo)",
    "total": "$77/mo base + $20 Resend"
  }
}
```

### Hub Service Architecture

```
hub/
├── src/
│   ├── server.ts              // Express setup
│   ├── api/
│   │   ├── routes/
│   │   │   ├── auth.ts        // Authentication endpoints
│   │   │   ├── trips.ts       // Trip CRUD operations
│   │   │   ├── import.ts      // AI parsing endpoints
│   │   │   ├── share.ts       // Sharing system
│   │   │   └── booking.ts     // Price tracking & affiliate logic
│   │   └── middleware/
│   │       ├── auth.ts        // JWT validation
│   │       ├── rateLimit.ts   // API rate limiting
│   │       └── cache.ts       // Response caching
│   ├── services/
│   │   ├── parser/
│   │   │   ├── regex.ts       // Free parsing tier
│   │   │   ├── local.ts       // Local LLM tier
│   │   │   └── openrouter.ts  // Premium parsing via OpenRouter
│   │   ├── affiliates/
│   │   │   ├── manager.ts     // Provider selection logic
│   │   │   ├── duffel.ts      // US domestic flights
│   │   │   ├── travelpayouts.ts // International flights
│   │   │   ├── hotels.ts      // Hotel providers
│   │   │   └── aggregator.ts  // Combines results
│   │   ├── pricing/
│   │   │   ├── tracker.ts     // Price monitoring
│   │   │   ├── rules.ts       // $10 rule implementation
│   │   │   └── cache.ts       // Smart caching strategy
│   │   └── sharing/
│   │       ├── links.ts       // Shareable link generation
│   │       └── viral.ts       // Viral tracking
│   ├── workers/
│   │   ├── priceCheck.ts      // Background price monitoring
│   │   ├── emailAlert.ts      // Notification sending
│   │   └── cleanup.ts         // Data maintenance
│   └── lib/
│       ├── database.ts        // Prisma client
│       ├── redis.ts           // Cache client
│       └── queue.ts           // Bull queue setup
```

### Intelligent Caching Strategy

```javascript
class CacheManager {
  constructor() {
    this.layers = {
      memory: new Map(),        // In-process, 10ms
      redis: new Redis(),       // Distributed, 1ms
      database: new Prisma()    // Persistent, 10ms
    };
  }
  
  async getFlightPrices(route, dates, isUSdomestic) {
    const key = `flight:${route}:${dates}:${isUSdomestic ? 'us' : 'intl'}`;
    
    // L1: Memory cache (instant)
    if (this.layers.memory.has(key)) {
      return this.layers.memory.get(key);
    }
    
    // L2: Redis cache (fast)
    const cached = await this.layers.redis.get(key);
    if (cached) {
      this.layers.memory.set(key, cached);
      return cached;
    }
    
    // L3: Check if hot route
    if (this.isHotRoute(route)) {
      return await this.getPreCachedPrice(route, dates);
    }
    
    // Fresh API call to appropriate provider
    const provider = isUSdomestic ? 'duffel' : 'travelpayouts';
    return await this.fetchFreshPrices(route, dates, provider);
  }
}
```

### Affiliate Integration Strategy

```javascript
class FlightProviderManager {
  async searchFlights(params) {
    const { origin, destination, dates } = params;
    const isUSdomestic = this.isUSdomesticRoute(origin, destination);
    
    if (isUSdomestic) {
      // Use Duffel for US domestic flights
      return await duffelAPI.searchFlights({
        origin,
        destination,
        departure_date: dates.departure,
        return_date: dates.return,
        passengers: params.passengers
      });
    } else {
      // Use Travelpayouts for international
      return await travelpayouts.searchFlights({
        origin,
        destination,
        depart_date: dates.departure,
        return_date: dates.return
      });
    }
  }
  
  isUSdomesticRoute(origin, destination) {
    const usAirports = ['JFK', 'LAX', 'ORD', 'DFW', 'ATL', 'SFO', 'SEA', 'BOS', 'LAS', 'MCO'];
    return usAirports.includes(origin) && usAirports.includes(destination);
  }
}
```

---

## Business Model & Metrics

### Monetization Strategy

#### Revenue Streams

**1. Invisible Affiliate Commissions (Primary)**
- **Flights**: 
  - Domestic: ~$6.50/booking (Duffel)
  - International: ~$12.80/booking (Travelpayouts)
- **Hotels**: ~$15/booking (4-6% of booking value)
- **User Experience**: Seamless booking with best prices
- **Projected**: $5,000-15,000/month at 10k users

**2. Premium Subscriptions (Secondary)**
- Free tier with generous limits
- Pro at $4.99/month
- Teams at $7.99/user/month
- Target: 15% conversion rate

### Key Metrics

#### User Acquisition
- **Target**: 10,000 users in 6 months
- **Channels**: SEO (40%), Referral (30%), Social (20%), Direct (10%)
- **CAC Target**: <$5 per user
- **Viral Coefficient**: 0.5+ (each user brings 0.5 new users)

#### Engagement Metrics
- **Day 1 Retention**: 80% (complete first import)
- **Day 7 Retention**: 40% (create/edit trip)
- **Day 30 Retention**: 25% (active usage)
- **Power Users**: 10% (3+ trips per month)

#### Revenue Metrics
- **Affiliate Revenue**: 
  - Domestic flights: ~$13 per booking (Duffel)
  - International flights: ~$8 per booking (Travelpayouts)
  - Hotels: ~$15 per booking (4-6% of booking value)
- **Free to Paid**: 15% conversion
- **MRR Target**: $8,649 by month 6 (combined revenue)
- **Churn Rate**: <5% monthly
- **LTV:CAC Ratio**: 3:1 minimum

#### Feature Usage Metrics
- **Import Success Rate**: 95%+
- **Share-to-Copy Rate**: 20%
- **Price Alert → Booking**: 8%
- **Mobile vs Web**: 60/40 split
- **Affiliate Link CTR**: 35% (when shown)

---

## Design System & UX Principles

### Visual Design Language

**Design Principles:**
1. **Clarity First**: Every element has clear purpose
2. **Speed Optimized**: Instant feedback for all actions
3. **Mobile Native**: Touch-first interactions
4. **Accessibility**: WCAG AA compliance minimum
5. **Delightful Details**: Micro-animations that inform

**Color System:**
```scss
// Primary Palette
$primary-blue: #2563EB;      // Actions, links
$primary-blue-light: #DBEAFE; // Backgrounds
$success-green: #10B981;      // Confirmations
$warning-amber: #F59E0B;      // Alerts
$error-red: #EF4444;          // Errors

// Neutral Palette  
$gray-900: #111827;           // Primary text
$gray-600: #4B5563;           // Secondary text
$gray-300: #D1D5DB;           // Borders
$gray-50: #F9FAFB;            // Backgrounds

// Semantic Colors
$flight-color: #6366F1;       // Indigo
$hotel-color: #8B5CF6;        // Purple  
$activity-color: #EC4899;     // Pink
$restaurant-color: #F97316;   // Orange
```

**Typography:**
```scss
// Font Stack
$font-primary: 'Inter', -apple-system, sans-serif;
$font-mono: 'JetBrains Mono', monospace;

// Type Scale
$text-xs: 0.75rem;    // 12px - metadata
$text-sm: 0.875rem;   // 14px - body small
$text-base: 1rem;     // 16px - body default  
$text-lg: 1.125rem;   // 18px - subheadings
$text-xl: 1.25rem;    // 20px - headings
$text-2xl: 1.5rem;    // 24px - page titles
```

### Interaction Patterns

**Gesture Library:**
- **Swipe right**: Archive/complete
- **Swipe left**: Delete/remove
- **Long press**: Quick actions menu
- **Pinch**: Zoom timeline/map
- **Double tap**: Quick edit mode

**Animation Specifications:**
```typescript
const animations = {
  microInteractions: {
    duration: '200ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
    examples: ['button press', 'checkbox', 'toggle']
  },
  
  transitions: {
    duration: '300ms',
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)', 
    examples: ['page routes', 'modal open', 'tab switch']
  },
  
  delightful: {
    duration: '600ms',
    easing: 'spring(1, 100, 10, 0)',
    examples: ['confetti', 'success states', 'onboarding']
  }
};
```

---

## Implementation Roadmap

### Phase 1: Foundation & Hub Setup (Weeks 1-4)
**Goal**: Core infrastructure with centralized hub architecture

**Week 1-2: Hub Infrastructure**
- [ ] Set up Node.js/Express hub on Render
- [ ] Configure Supabase PostgreSQL + Auth
- [ ] Implement JWT authentication in hub
- [ ] Set up Redis caching (Upstash free tier)
- [ ] Create basic API structure

**Week 3-4: AI Import Engine**
- [ ] Build 3-tier parsing system in hub
- [ ] Regex parser for common patterns
- [ ] Integrate OpenRouter API
- [ ] Create import UI in Next.js
- [ ] Test with real AI conversations

### Phase 2: Core Features & Affiliates (Weeks 5-8)
**Goal**: Timeline, map, sharing, and invisible affiliate integration

**Week 5-6: Visualization & Sharing**
- [ ] Timeline component with drag-drop
- [ ] Mapbox integration with photo markers
- [ ] Hub manages all trip data operations
- [ ] Shareable link generation
- [ ] Viral tracking system

**Week 7-8: Affiliate Integration**
- [ ] Integrate Duffel API for US flights
- [ ] Integrate Travelpayouts for international
- [ ] Implement provider selection logic
- [ ] Build price tracking jobs
- [ ] Test booking flow end-to-end

### Phase 3: Mobile & Polish (Weeks 9-12)
**Goal**: Mobile app with offline support and subscription system

**Week 9-10: React Native App**
- [ ] Expo setup with Mapbox
- [ ] Offline storage implementation
- [ ] Map offline mode
- [ ] Push notifications via hub
- [ ] Test sync scenarios

**Week 11-12: Monetization**
- [ ] Stripe integration in hub
- [ ] Subscription management
- [ ] Usage limits enforcement
- [ ] Upgrade flows
- [ ] Affiliate tracking refinement

### Phase 4: Launch Prep (Weeks 13-14)
**Goal**: Beta launch readiness

- [ ] Performance optimization
- [ ] Security audit
- [ ] Beta user onboarding
- [ ] Documentation
- [ ] Marketing site
- [ ] Seed viral sharing content

---

## Risk Mitigation

### Technical Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Hub becomes bottleneck | High | Medium | Implement caching layers, prepare horizontal scaling plan |
| AI API costs exceed budget | High | Medium | 3-tier parsing system, OpenRouter for model flexibility, aggressive caching |
| Dual flight API complexity | Medium | Low | Clear provider routing logic, fallback mechanisms |
| Complex itineraries fail parsing | High | Low | Manual correction UI, continuous model training |
| Offline sync conflicts | Medium | High | Version control system, clear conflict UI |

### Business Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Low affiliate conversions | High | Medium | A/B test booking flows, optimize provider selection |
| Flight coverage gaps | Medium | Low | Dual-provider strategy mitigates this |
| Low viral coefficient | High | Medium | Incentive system, better sharing UX, template marketplace |
| Competition copies features | Medium | High | Build community moat, move faster, focus on UX |
| Low free-to-paid conversion | High | Medium | Test pricing, clear value prop, usage-based limits |

### Hub-Specific Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Single point of failure | High | Low | Health checks, auto-restart, error boundaries |
| Deployment complexity | Low | Low | Blue-green deployments, rollback strategy |
| Debugging difficulties | Medium | Low | Comprehensive logging, distributed tracing |

---

## Success Criteria

### Launch Criteria
- [ ] 95%+ import success rate for AI conversations
- [ ] <3 second page load times
- [ ] <5 second import processing via hub
- [ ] 99.9% hub uptime
- [ ] Affiliate integration tested end-to-end
- [ ] Mapbox offline maps working
- [ ] Mobile app store approval

### 6-Month Success Metrics
- [ ] 10,000+ registered users
- [ ] 20%+ monthly active users
- [ ] $5,000+ monthly affiliate revenue
- [ ] 15%+ paid conversion rate
- [ ] 0.5+ viral coefficient
- [ ] 4.5+ app store rating
- [ ] $8,649+ total MRR (affiliate + subscriptions)

---

## Appendices

### A. Competitive Analysis

| Feature | TravelViz | TripIt | Wanderlog | Roadtrippers |
|---------|-----------|--------|-----------|--------------|
| AI Import | ✅ Universal | ❌ Email only | ❌ Manual | ❌ Manual |
| Visual Timeline | ✅ Interactive | ⚠️ List view | ✅ Basic | ❌ No |
| Map Integration | ✅ Mapbox Advanced | ⚠️ Basic | ✅ Good | ✅ Excellent |
| Offline Mobile | ✅ Full with Mapbox | ✅ Good | ⚠️ Limited | ❌ No |
| Price Tracking | ✅ Dual-provider | ❌ No | ❌ No | ❌ No |
| Viral Sharing | ✅ Built-in | ❌ No | ⚠️ Basic | ⚠️ Basic |
| Affiliate Revenue | ✅ Invisible | ❌ No | ⚠️ Visible | ⚠️ Visible |
| Free Tier | ✅ Generous | ⚠️ Limited | ✅ Good | ⚠️ Limited |

### B. Hub Architecture Benefits

**Development Velocity**
- Single codebase to maintain
- Unified logging and monitoring
- Easier debugging and testing
- Faster feature deployment

**Cost Efficiency**
- One service to scale ($25/mo on Render)
- Shared infrastructure resources
- Simplified DevOps requirements
- Lower operational overhead

**Technical Advantages**
- Consistent business logic
- Centralized security
- Single source of truth
- Easy API versioning

### C. Affiliate Integration Details

**Duffel Flight Integration (US Domestic)**
```javascript
// Simple integration for US flights
const searchDomesticFlights = async (params) => {
  const response = await duffel.offerRequests.create({
    slices: [{
      origin: params.origin,
      destination: params.destination,
      departure_date: params.date
    }],
    passengers: params.passengers,
    cabin_class: params.class || 'economy'
  });
  
  // Add our markup
  return response.offers.map(offer => ({
    ...offer,
    totalPrice: offer.total_amount + 3.00, // $3 flat fee
    bookingLink: generateDuffelBookingLink(offer.id)
  }));
};
```

**Travelpayouts Integration (International)**
```javascript
// International flight search
const searchInternationalFlights = async (params) => {
  const response = await fetch(
    `${TRAVELPAYOUTS_API}/v2/prices/latest`,
    {
      headers: { 'X-Access-Token': process.env.TRAVELPAYOUTS_TOKEN },
      params: {
        origin: params.from,
        destination: params.to,
        departure_at: params.date,
        return_at: params.returnDate,
        currency: 'USD'
      }
    }
  );
  return response.data;
};
```

**Revenue Tracking**
```javascript
// Hub tracks all conversions by provider
const trackConversion = async (clickId, userId, amount, provider) => {
  const commission = provider === 'duffel' 
    ? 3.00 + (amount * 0.01) // $3 + 1%
    : amount * 0.016;         // 1.6% for Travelpayouts
    
  await db.conversions.create({
    data: {
      clickId,
      userId,
      amount,
      commission,
      provider,
      timestamp: new Date()
    }
  });
};
```

### D. User Interview Insights

**Key Quotes from User Research:**

> "I had this perfect 2-week Italy trip planned with ChatGPT, then cleared my browser history by accident. 3 hours of work gone." - Designer, 29

> "My friends loved my AI-planned Barcelona trip so much, they all wanted copies. I ended up copy-pasting the same thing 8 times." - Teacher, 34

> "The worst part is going from Claude's beautiful itinerary to having 20 booking tabs open, trying to match dates and times." - Consultant, 41

> "I never know if I'm getting the best price when I book from all these different sites." - Product Manager, 35

> "I wish I could see everything on a map and timeline - AI gives me a list but I can't visualize the flow." - Marketing Director, 38

---

*This PRD serves as the single source of truth for TravelViz development. The hub architecture, dual-provider flight strategy, and Mapbox integration create a sustainable business while delivering exceptional user value.*

**Document Status**: Living document, updated weekly  
**Last Updated**: December 2024  
**Version**: 2.1  
**Owner**: Product Team  
**Architecture**: Hub-centric with dual-provider affiliate monetization