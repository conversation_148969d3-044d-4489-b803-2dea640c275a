# Technical Requirements Document (TRD)
## TravelBuddy MVP - Version 1.1

---

## Document Control

**Version:** 1.1  
**Date:** December 2024  
**Status:** Updated with PRD v2.1 changes  
**Owner:** Product Development Team  
**Classification:** Internal

### Revision History

| Version | Date | Author | Description |
|---------|------|--------|-------------|
| 1.0 | Dec 2024 | Product Team | Initial draft |
| 1.1 | Dec 2024 | Product Team | Added hub architecture, dual-provider strategy, enhanced features |

---

## Executive Summary

TravelBuddy (domain: travelviz.ai) is a comprehensive travel companion application designed to eliminate the fragmentation users experience when planning trips across 15+ separate tabs and applications. This Technical Requirements Document defines the specifications for a Minimum Viable Product (MVP) that combines intelligent AI-powered import capabilities, intuitive trip organization, seamless sharing features, and a hub-centric backend architecture.

The dual-platform solution consists of a feature-complete web application and a companion mobile application optimized for offline viewing with Mapbox integration. The system implements a centralized hub architecture using Node.js/Express for all business logic, with dual flight provider coverage (Duffel for US domestic, Travelpayouts for international). With a 14-week development timeline and operational budget of $97/month, TravelBuddy aims to acquire 10,000 users within 6 months of launch.

This document serves as the single source of truth for all technical decisions, feature specifications, and implementation requirements throughout the development lifecycle.

---

## System Overview

### Product Vision

TravelBuddy revolutionizes trip planning by consolidating scattered travel information into a single, intelligent platform. Users can import itineraries from AI assistants (ChatGPT/Claude/Gemini), organize them visually with timelines and Mapbox-powered maps, track prices through dual flight providers, and share complete trips with viral mechanics.

### Architecture Overview

#### Hub-Centric Architecture (NEW)

```
┌─────────────────────────────────────────────────┐
│                   CLIENTS                       │
├─────────────────┬───────────────────────────────┤
│   Next.js Web   │   React Native Mobile         │
└────────┬────────┴────────────┬──────────────────┘
         │                     │
         ↓                     ↓
┌─────────────────────────────────────────────────┐
│           THE HUB (Node.js/Express)             │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │          API Gateway Layer               │   │
│  │  • JWT Authentication                    │   │
│  │  • Rate Limiting                         │   │
│  │  • Request Validation                    │   │
│  │  • Error Handling                        │   │
│  └─────────────────────────────────────────┘   │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │         Business Logic Core              │   │
│  │  • Trip Management Service               │   │
│  │  • AI Parsing Engine (3-tier)            │   │
│  │  • Sharing System                        │   │
│  │  • Price Tracking Engine                 │   │
│  │  • Dual Provider Affiliate System        │   │
│  └─────────────────────────────────────────┘   │
│                                                 │
│  ┌─────────────────────────────────────────┐   │
│  │         Service Layer                    │   │
│  │  • Database Queries (Prisma)             │   │
│  │  • Cache Management (Redis)              │   │
│  │  • External API Orchestration            │   │
│  │  • Background Job Queue (Bull)           │   │
│  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────┘
         │                     │
         ↓                     ↓
┌─────────────────┐   ┌───────────────────────┐
│   PostgreSQL    │   │   External APIs       │
│   (Supabase)    │   │   • OpenRouter AI     │
│                 │   │   • Duffel (US)       │
│                 │   │   • Travelpayouts     │
│                 │   │   • Hotels.com        │
│                 │   │   • Mapbox            │
└─────────────────┘   └───────────────────────┘
```

### Key Components

1. **Web Application**: Full-featured trip planning and management interface
2. **Mobile Application**: Offline-capable viewer with Mapbox integration
3. **The Hub**: Centralized Node.js/Express service handling all business logic (NEW)
4. **AI Parser Engine**: Three-tier parsing system (Regex → Local LLM → OpenRouter) (ENHANCED)
5. **Trip Organizer**: Timeline and Mapbox-powered visualization system (ENHANCED)
6. **Dual Flight Providers**: Duffel for US domestic, Travelpayouts for international (NEW)
7. **Viral Sharing Engine**: One-click sharing with copy tracking (ENHANCED)
8. **Smart Affiliate System**: Invisible monetization with $10 rule (NEW)
9. **Price Tracker**: Real-time flight and hotel price monitoring via dual providers
10. **User Management**: Authentication and subscription handling

---

## Functional Requirements

### FR-001: Universal AI Import System (ENHANCED)

**Priority:** P0  
**Dependencies:** Hub infrastructure, OpenRouter API integration  

#### User Story
**As a** traveler who uses AI assistants for trip planning  
**I want to** paste my AI conversation directly into TravelBuddy  
**So that** my itinerary is automatically parsed and organized in 30 seconds or less  

#### Acceptance Criteria

```gherkin
Scenario: Three-tier parsing system (NEW)
Given I paste AI-generated content
When the hub processes the content
Then it attempts regex parsing first (free)
And falls back to local LLM if confidence < 0.8 ($0.02)
And uses OpenRouter API only for complex cases ($0.10)
And maintains 95%+ accuracy overall
And completes within 5 seconds
And tracks parsing costs per request

Scenario: Cost optimization (NEW)
Given 1000 parsing requests
When processed through the three-tier system
Then 60% are handled by regex (free)
And 30% by local LLM ($0.02 each)
And 10% by OpenRouter API ($0.10 each)
And average cost per parse is < $0.05

Scenario: Multi-format support
Given I have travel information from an AI assistant
When I paste content in any of these formats:
  - Direct conversation text
  - Claude conversation URLs (NEW)
  - ChatGPT shared links (NEW)
  - Gemini conversations (NEW)
  - Markdown formatted itinerary
  - Bulleted lists with dates
  - Natural language descriptions
  - Email forwards (NEW)
Then the system successfully parses all formats
And maintains the original context and relationships
```

#### Technical Considerations

- Implement three-tier NLP parsing via the hub (NEW)
- Support multiple AI conversation formats and styles
- Cache parsed results in Redis for performance (NEW)
- Track parsing method and costs (NEW)
- Store original content for re-parsing capabilities

#### Hub Implementation (NEW)

```typescript
// Hub manages parsing with cost optimization
async function parseImport(content: string) {
  // Tier 1: Try regex (FREE)
  const regexResult = await regexParser.parse(content);
  if (regexResult.confidence > 0.8) {
    await trackParsing('regex', 0);
    return regexResult;
  }
  
  // Tier 2: Try local model ($0.02)
  const localResult = await localLLM.parse(content);
  if (localResult.confidence > 0.8) {
    await trackParsing('local', 0.02);
    return localResult;
  }
  
  // Tier 3: Use OpenRouter API ($0.10)
  const apiResult = await openRouterAPI.parse(content, {
    models: ['claude-3-opus', 'gpt-4', 'gemini-pro'],
    maxCost: 0.10
  });
  await trackParsing('openrouter', 0.10);
  return apiResult;
}
```

### FR-002: Trip Organization System (ENHANCED WITH MAPBOX)

**Priority:** P0  
**Dependencies:** Mapbox API, Hub trip management  

#### User Story
**As a** trip planner  
**I want to** see my itinerary as both a timeline and on a Mapbox map with custom photo markers  
**So that** I can understand my trip flow spatially and temporally with offline capability  

#### Acceptance Criteria

```gherkin
Scenario: Mapbox integration (NEW)
Given I have a trip with locations
When I view the map
Then I see custom HTML markers with photos
And numbered markers by day with color gradients
And routes drawn between locations using Mapbox Directions
And the map style is optimized for travel
And I can download offline maps up to 6GB

Scenario: Timeline view
Given I have an imported or created itinerary
When I access the timeline view
Then I see all activities chronologically ordered
And each day is clearly separated
And I can drag-and-drop to reorder activities
And changes persist immediately via the hub

Scenario: Synchronized views
Given I am viewing my trip
When I select an activity in timeline view
Then the Mapbox map flies to that location smoothly
And highlights the custom photo marker
And shows walking/transit/driving routes
And vice versa for map-to-timeline selection
```

#### Technical Considerations

- Implement real-time synchronization between views via hub
- Use Mapbox GL JS v3 for web (NEW)
- Use Mapbox SDK for React Native mobile (NEW)
- Enable offline map caching up to 6GB (NEW)
- Custom HTML markers with photos (NEW)
- Support multiple trip legs with different transport modes

### FR-003: Viral Sharing System (ENHANCED)

**Priority:** P0  
**Dependencies:** Hub sharing service, Trip storage  

#### User Story
**As a** trip creator  
**I want to** share my entire trip with one beautiful link  
**So that** others can copy and customize it for themselves, creating viral growth  

#### Acceptance Criteria

```gherkin
Scenario: Generate shareable link
Given I have a completed trip itinerary
When I click "Share Trip"
Then the hub generates a unique link (travelviz.ai/t/[8-chars]) (NEW)
And creates a dynamic OG image showing the map (NEW)
And I can copy it with one click
And set privacy options (public/unlisted/private)
And track how many times it's been viewed and copied

Scenario: Frictionless copying (NEW)
Given I receive a TravelBuddy share link
When I click it without being logged in
Then I see a beautiful preview of the trip
And a prominent "Copy This Trip" button
And can copy without creating an account first
And get prompted to sign up after copying
And the hub tracks the viral chain

Scenario: Viral tracking (ENHANCED)
Given users are sharing and copying trips
When someone copies a shared trip
Then the original creator sees anonymized stats
And popular trips appear in trending templates (NEW)
And creators earn viral badges
And viral coefficient is tracked (target 0.5+) (NEW)
```

#### Technical Considerations

- Implement secure share URLs via hub
- Generate dynamic OG images for social sharing (NEW)
- Track sharing analytics for viral growth
- Enable copying without login (soft signup after) (NEW)
- Consider implementing trip templates marketplace

### FR-004: Dual-Provider Price Tracking System (NEW)

**Priority:** P1  
**Dependencies:** Duffel API, Travelpayouts API, Hub affiliate service  

#### User Story
**As a** budget-conscious traveler  
**I want to** track flight and hotel prices with the best coverage  
**So that** I can book when prices drop while TravelBuddy earns sustainable revenue  

#### Acceptance Criteria

```gherkin
Scenario: Dual flight provider system (NEW)
Given I search for flights
When the hub determines the route
Then it uses Duffel API for US domestic flights
And Travelpayouts for international flights
And seamlessly combines results when needed
And applies the $10 rule for affiliate links

Scenario: The $10 rule implementation (NEW)
Given multiple flight options exist
When displaying booking options
Then show the absolute lowest price first
And highlight affiliate options within $10 of lowest
And prioritize by commission rate if multiple qualify
And never compromise user trust for revenue

Scenario: Smart caching strategy (NEW)
Given popular routes are searched frequently
When users search for flights
Then the hub uses 3-tier caching:
  - In-memory cache (5 min TTL)
  - Redis cache (30 min TTL)
  - Database cache (6 hour TTL)
And reduces API costs by 70%
And serves results in <200ms for cached routes

Scenario: Price drop alerts
Given I'm tracking prices for my trip
When a price drops by >5% or $50
Then I receive an email notification via hub
And push notification on mobile
And the app highlights the price drop
And shows a direct booking link with affiliate tracking
```

#### Hub Implementation (NEW)

```typescript
// Hub manages dual-provider logic
class FlightProviderManager {
  async searchFlights(params) {
    const isUSdomestic = this.isUSRoute(params.origin, params.destination);
    
    if (isUSdomestic) {
      // Use Duffel for US domestic
      return await duffelAPI.searchFlights({
        ...params,
        markup: 3.00 // $3 flat fee + 1%
      });
    } else {
      // Use Travelpayouts for international
      return await travelpayouts.searchFlights({
        ...params,
        commission: 0.016 // 1.6% average
      });
    }
  }
  
  applyTenDollarRule(options: FlightOption[]) {
    const lowest = Math.min(...options.map(o => o.price));
    
    return options.filter(opt => {
      const diff = opt.price - lowest;
      return diff <= 10 && opt.hasAffiliate;
    }).sort((a, b) => b.commission - a.commission);
  }
}
```

#### Technical Considerations

- Implement efficient batch API calls via hub
- Smart provider selection based on route
- Store price history in time-series optimized format
- Create background jobs for price checking
- Implement affiliate link tracking (invisible to users)

### FR-005: Offline Mobile Experience (ENHANCED WITH MAPBOX)

**Priority:** P0  
**Dependencies:** React Native, Mapbox SDK, SQLite  

#### User Story
**As a** traveler without reliable internet  
**I want to** access my full itinerary offline with Mapbox maps  
**So that** I can navigate anywhere during my trip  

#### Acceptance Criteria

```gherkin
Scenario: Mapbox offline sync (NEW)
Given I have internet connection
When I open a trip in the mobile app
Then all trip data downloads automatically
And Mapbox tiles cache for the trip area (up to 6GB)
And custom markers and routes are stored
And documents/confirmations store locally
And sync status shows clearly

Scenario: Offline functionality
Given I'm offline with cached trip data
When I open the mobile app
Then I can view complete itinerary
And navigate with GPS on Mapbox offline maps (NEW)
And see all custom photo markers (NEW)
And access all reservation details
And make notes (sync when online)

Scenario: Smart sync
Given I return online after offline usage
When the app detects connectivity
Then any offline changes sync via the hub (NEW)
And conflicts resolve intelligently
And sync completes in background
And user sees sync success confirmation
```

#### Technical Considerations

- Implement SQLite for complex offline data
- Use Mapbox offline regions API (NEW)
- Store map tiles up to zoom level 17 (NEW)
- Create intelligent sync queue via hub
- Optimize storage with progressive download

### FR-006: Freemium Subscription Model (ENHANCED WITH DUAL REVENUE)

**Priority:** P0  
**Dependencies:** Hub subscription service, Stripe, Affiliate tracking  

#### User Story
**As a** product owner  
**I want to** monetize through invisible affiliates (70%) and subscriptions (30%)  
**So that** we achieve sustainable MRR of $8,649+ within 6 months  

#### Acceptance Criteria

```gherkin
Scenario: Dual revenue streams (NEW)
Given our monetization model
When tracking monthly revenue
Then affiliate commissions provide 70% of revenue
And subscriptions provide 30% of revenue
And combined MRR exceeds $8,649 by month 6
And users never see "affiliate" mentioned

Scenario: Invisible affiliate integration (NEW)
Given users book flights/hotels
When they click booking links
Then affiliate tracking happens transparently
And users always see the best price
And the $10 rule ensures trust
And conversion tracking is accurate

Scenario: Free tier limitations
Given I'm a free user
When I use TravelBuddy
Then I can create up to 3 trips
And access basic features
And set 5 price alerts per trip (NEW)
And see upgrade prompts at limits
And understand Pro benefits clearly

Scenario: Pro upgrade flow ($4.99/mo)
Given I want to upgrade to Pro
When I click upgrade
Then I see clear benefit comparison
And secure payment via Stripe
And immediate access to:
  - Unlimited trips
  - Unlimited price alerts
  - Advanced AI parsing (NEW)
  - No watermarks on shares (NEW)
  - 30-day offline access (NEW)
  - Priority support

Scenario: Teams subscription ($7.99/user/mo)
Given I want Teams plan
When I subscribe to Teams
Then I can invite team members
And share trips within organization
And access team analytics
And manage team billing centrally
And get API access (NEW)
```

#### Technical Considerations

- Integrate Stripe via hub for payment processing
- Implement invisible affiliate tracking (NEW)
- Track conversion metrics without user awareness (NEW)
- Create upgrade/downgrade flows
- Handle payment failures gracefully

---

## Non-Functional Requirements

### NFR-001: Performance Requirements

**Hub Performance (NEW):**
- API Response Time: < 200ms (P95)
- Parsing Time: < 5 seconds for complex imports
- Concurrent Requests: 1000+ per second
- Cache Hit Rate: > 70% for popular routes

**Web Application:**
- Page Load Time: < 2 seconds (P90)
- Time to Interactive: < 1.5 seconds
- API Response Time: < 500ms (P95)
- Lighthouse Score: > 90
- Mapbox Map Load: < 1 second (NEW)

**Mobile Application:**
- App Launch Time: < 2 seconds
- Screen Transition: < 300ms
- Memory Usage: < 200MB app + 6GB maps (UPDATED)
- Battery Usage: < 5% per hour active use
- Offline Map Performance: 60 FPS (NEW)

**System-wide:**
- Support 10,000 concurrent users
- 99.9% uptime SLA
- < 50ms database query time (IMPROVED)
- < 5 second AI parsing time
- < $0.05 average parsing cost (NEW)

### NFR-002: Scalability Requirements

**Hub Scalability (NEW):**
- Horizontal scaling on Render
- Stateless design for easy scaling
- Redis distributed caching
- Queue-based background jobs

**System Scalability:**
- Auto-scaling based on load
- CDN for static assets
- Database connection pooling via hub (NEW)
- Caching strategy for common queries
- Support growth to 100,000 users

### NFR-003: Security Requirements

**Authentication & Authorization:**
- JWT-based authentication via Supabase Auth
- Hub validates all tokens (NEW)
- Role-based access control
- OAuth2 social login support

**Data Protection:**
- TLS 1.3 for all hub communications (NEW)
- AES-256 encryption at rest
- PII data anonymization
- GDPR compliance
- No storage of payment details (NEW)

**API Security:**
- Rate limiting per user/IP in hub (NEW)
- API key rotation for external services
- Request signing
- Input validation in hub layer (NEW)
- SQL injection prevention via Prisma (NEW)

### NFR-004: Usability Requirements

- WCAG 2.1 AA accessibility compliance
- Mobile-first responsive design
- Support for 5 languages at launch
- Maximum 3 clicks to any feature
- Consistent UI/UX across platforms
- Comprehensive help documentation
- Offline-first mobile experience (NEW)

### NFR-005: Reliability Requirements

- Automated backup every 6 hours
- Point-in-time recovery capability
- Disaster recovery plan
- Graceful degradation via hub (NEW)
- Circuit breakers for external APIs in hub (NEW)
- Comprehensive error logging
- Offline sync conflict resolution (NEW)

---

## System Architecture

### Technology Stack

**The Hub - Backend (NEW):**
- Runtime: Node.js 20.x LTS
- Framework: Express.js with TypeScript
- ORM: Prisma
- Cache: Redis (Upstash)
- Queue: Bull
- Deployment: Render

**Frontend - Web:**
- Framework: Next.js 14 (App Router)
- Language: TypeScript 5.x
- Styling: Tailwind CSS 3.x
- UI Components: Shadcn/ui
- State Management: Zustand
- Forms: React Hook Form + Zod
- Maps: Mapbox GL JS v3 (NEW)
- API Client: React Query (NEW)

**Frontend - Mobile:**
- Framework: React Native 0.73+
- Platform: Expo SDK 50
- Navigation: React Navigation 6
- State: Redux Toolkit + RTK Query
- Storage: AsyncStorage + SQLite
- Maps: Mapbox SDK (NEW)
- Offline: SQLite + Mapbox Offline (NEW)

**Backend Infrastructure:**
- Platform: Supabase (Database + Auth only) (UPDATED)
- Hub: Node.js on Render (NEW)
- Database: PostgreSQL 15
- Authentication: Supabase Auth
- Storage: Supabase Storage
- Cache: Redis via Upstash (NEW)
- Queue: Bull on Render Worker (NEW)

**External Services:**
- AI Processing: OpenRouter (Claude/GPT-4/Gemini) (ENHANCED)
- US Flights: Duffel API (NEW)
- International Flights: Travelpayouts (NEW)
- Hotels: Hotels.com via Expedia (NEW)
- Maps: Mapbox GL (NEW)
- Payments: Stripe
- Email: Resend (NEW)
- Analytics: Plausible
- Monitoring: Sentry

### Database Schema (ENHANCED)

```sql
-- Core Tables with enhancements for hub architecture

-- User profiles (extends Supabase Auth)
CREATE TABLE profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id),
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free',
    subscription_status TEXT DEFAULT 'active',
    stripe_customer_id TEXT,
    viral_score INTEGER DEFAULT 0, -- NEW
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Trips with enhanced tracking
CREATE TABLE trips (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    start_date DATE,
    end_date DATE,
    is_public BOOLEAN DEFAULT false,
    share_token TEXT UNIQUE,
    share_url TEXT, -- NEW: travelviz.ai/t/[token]
    parent_trip_id UUID REFERENCES trips(id),
    copy_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0, -- NEW
    
    -- Import tracking
    import_source TEXT,
    import_raw_text TEXT,
    parsing_method TEXT, -- NEW: 'regex', 'local', 'openrouter'
    parsing_cost DECIMAL(10, 4), -- NEW
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Activities with Mapbox and affiliate support
CREATE TABLE activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    trip_id UUID NOT NULL REFERENCES trips(id) ON DELETE CASCADE,
    trip_day_id UUID REFERENCES trip_days(id) ON DELETE CASCADE,
    
    -- Basic info
    title TEXT NOT NULL,
    description TEXT,
    category TEXT,
    photo_url TEXT, -- NEW: for Mapbox custom markers
    
    -- Timing
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    duration_minutes INTEGER,
    
    -- Location with Mapbox support
    location_name TEXT,
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    mapbox_place_id TEXT, -- NEW
    
    -- Booking info
    booking_status TEXT DEFAULT 'not_booked',
    confirmation_number TEXT,
    booking_url TEXT,
    
    -- Price tracking
    price DECIMAL(10, 2),
    currency TEXT DEFAULT 'USD',
    is_tracking_price BOOLEAN DEFAULT false,
    
    -- Affiliate tracking (NEW)
    affiliate_provider TEXT, -- 'duffel', 'travelpayouts', 'hotels_com'
    affiliate_link TEXT,
    affiliate_click_id TEXT,
    is_us_domestic BOOLEAN, -- NEW: for provider selection
    
    -- Order
    display_order INTEGER DEFAULT 0,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enhanced price history
CREATE TABLE price_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
    price DECIMAL(10, 2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    source TEXT, -- NEW: which provider gave this price
    provider TEXT, -- NEW: 'duffel', 'travelpayouts', etc
    tracked_at TIMESTAMPTZ DEFAULT NOW()
);

-- Affiliate tracking (NEW)
CREATE TABLE affiliate_conversions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id),
    activity_id UUID REFERENCES activities(id),
    
    click_id TEXT UNIQUE NOT NULL,
    provider TEXT NOT NULL, -- 'duffel', 'travelpayouts', 'hotels_com'
    clicked_at TIMESTAMPTZ NOT NULL,
    
    -- Conversion tracking
    converted BOOLEAN DEFAULT false,
    converted_at TIMESTAMPTZ,
    booking_amount DECIMAL(10, 2),
    commission_amount DECIMAL(10, 2),
    commission_status TEXT DEFAULT 'pending',
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Cache management (NEW)
CREATE TABLE cache_entries (
    cache_key TEXT PRIMARY KEY,
    cache_value JSONB NOT NULL,
    cache_tier TEXT NOT NULL, -- 'memory', 'redis', 'database'
    expires_at TIMESTAMPTZ NOT NULL,
    hit_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User feeds for 7-day activation (NEW)
CREATE TABLE user_feeds (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES profiles(id),
    activated_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    search_count INTEGER DEFAULT 0,
    provider_preference TEXT -- 'duffel', 'travelpayouts', 'both'
);

-- Enable Row Level Security
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE price_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE affiliate_conversions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own trips" ON trips
    FOR ALL USING (auth.uid() = user_id OR is_public = true);

CREATE POLICY "Users can manage own activities" ON activities
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM trips 
            WHERE trips.id = activities.trip_id 
            AND trips.user_id = auth.uid()
        )
    );
```

### Hub API Design (NEW)

**RESTful Endpoints via Hub:**

```
# All requests go through the hub at hub.travelviz.ai

# Authentication (proxied to Supabase)
POST   /api/auth/signup
POST   /api/auth/login
POST   /api/auth/logout
GET    /api/auth/me

# AI Import (Hub's 3-tier system)
POST   /api/import/parse       # Parse AI content
GET    /api/import/status/:id  # Check parsing job

# Trips (Hub managed)
GET    /api/trips              # List user's trips
POST   /api/trips              # Create new trip
GET    /api/trips/:id          # Get trip details
PUT    /api/trips/:id          # Update trip
DELETE /api/trips/:id          # Delete trip
POST   /api/trips/:id/copy     # Copy shared trip

# Activities  
GET    /api/trips/:id/activities
POST   /api/trips/:id/activities
PUT    /api/activities/:id
DELETE /api/activities/:id
POST   /api/activities/:id/reorder

# Sharing (Hub's viral engine)
POST   /api/trips/:id/share    # Generate share link
GET    /api/share/:token       # Get shared trip
POST   /api/share/:token/copy  # Copy without auth

# Flight Search (Dual-provider system)
POST   /api/flights/search     # Smart provider selection
GET    /api/flights/cache/:id  # Get cached results

# Price Tracking (Hub background jobs)
POST   /api/activities/:id/track
GET    /api/activities/:id/prices
DELETE /api/activities/:id/track

# Affiliate Management (Internal only)
POST   /api/internal/affiliate/click
POST   /api/internal/affiliate/conversion
GET    /api/internal/affiliate/metrics
```

### Hub Service Architecture (NEW)

```
hub/
├── src/
│   ├── server.ts              // Express setup
│   ├── api/
│   │   ├── routes/
│   │   │   ├── auth.ts        // Supabase auth proxy
│   │   │   ├── trips.ts       // Trip CRUD
│   │   │   ├── import.ts      // 3-tier parsing
│   │   │   ├── flights.ts     // Dual-provider search
│   │   │   └── sharing.ts     // Viral mechanics
│   │   └── middleware/
│   │       ├── auth.ts        // JWT validation
│   │       ├── rateLimit.ts   // Per-user limits
│   │       └── cache.ts       // Response caching
│   ├── services/
│   │   ├── parser/
│   │   │   ├── regex.ts       // Tier 1: Free
│   │   │   ├── local.ts       // Tier 2: $0.02
│   │   │   └── openrouter.ts  // Tier 3: $0.10
│   │   ├── affiliates/
│   │   │   ├── duffel.ts      // US domestic
│   │   │   ├── travelpayouts.ts // International
│   │   │   └── aggregator.ts  // Combines & applies $10 rule
│   │   └── cache/
│   │       └── manager.ts     // 3-tier caching
│   └── workers/
│       ├── priceTracker.ts    // Background price checks
│       └── emailSender.ts     // Notifications
```

---

## Data Requirements

### Data Models

**User Data:**
- Profile information (email, name, preferences)
- Subscription details
- Authentication tokens
- Usage statistics
- Viral score and attribution (NEW)

**Trip Data:**
- Trip metadata (title, dates, description)
- Activities and events with Mapbox data (ENHANCED)
- Locations and routes
- Booking confirmations
- Price tracking preferences
- Sharing settings and viral metrics (NEW)
- Parsing cost tracking (NEW)

**Affiliate Data (NEW):**
- Click tracking with unique IDs
- Conversion attribution
- Provider performance metrics
- Revenue reconciliation
- Invisible to end users

**System Data:**
- Analytics events
- Error logs
- Performance metrics
- API usage statistics
- Cache hit rates (NEW)
- Parsing method distribution (NEW)

### Data Retention

- User data: Retained while account active + 30 days
- Trip data: Retained indefinitely for viral sharing (UPDATED)
- Price history: 90 days rolling window
- Parsing cache: 7 days (NEW)
- Affiliate data: 2 years for reconciliation (NEW)
- Logs: 30 days
- Analytics: Aggregated indefinitely

### Data Privacy

- GDPR compliant data handling
- User data export capability
- Right to deletion implementation
- Anonymized analytics only
- No third-party data sharing
- Affiliate tracking transparent in privacy policy (NEW)

---

## Testing Requirements

### Testing Strategy

**Hub Testing (NEW):**
- Jest for Node.js services
- 90% code coverage target
- Mock all external APIs
- Test all parsing tiers

**Unit Testing:**
- Minimum 80% code coverage
- Jest for JavaScript/TypeScript
- React Testing Library for components
- Mock external dependencies

**Integration Testing:**
- Hub API endpoint testing (NEW)
- Database operation testing via hub (NEW)
- External service integration
- Authentication flow testing
- Dual-provider flight search (NEW)

**End-to-End Testing:**
- Critical user journeys
- Cross-browser testing
- Mobile app user flows with Mapbox (NEW)
- Performance benchmarks
- Offline sync scenarios (NEW)

**User Acceptance Testing:**
- Beta testing program
- Feedback collection system
- A/B testing framework
- Usability studies

### Test Scenarios

**Critical Test Cases:**

1. **Three-Tier AI Import (NEW)**
   - Regex catches 60% of simple formats
   - Local LLM handles 30% medium complexity
   - OpenRouter for 10% edge cases
   - Cost tracking accuracy
   - 95%+ overall accuracy

2. **Dual Flight Provider System (NEW)**
   - Correct provider selection (US vs International)
   - $10 rule implementation
   - Cache hit rates >70%
   - Affiliate link generation
   - Invisible tracking confirmation

3. **Trip Sharing**
   - Generate share link (travelviz.ai/t/xxx)
   - Dynamic OG image generation (NEW)
   - Copy shared trip without auth (NEW)
   - Viral coefficient tracking (NEW)
   - Attribution accuracy

4. **Mapbox Integration (NEW)**
   - Custom photo markers
   - Offline map download
   - GPS navigation offline
   - Sync on reconnection
   - Storage optimization

5. **Payment Flow**
   - Free to Pro upgrade
   - Payment failure handling
   - Subscription cancellation
   - Team billing
   - Usage limit enforcement

---

## Deployment Requirements

### Infrastructure

**Production Environment:**
- Hub: Render ($25/month) (NEW)
- Database: Supabase ($25/month)
- Redis: Upstash (Free tier) (NEW)
- Worker: Render Worker ($7/month) (NEW)
- Web Hosting: Vercel ($20/month)
- Mobile: Expo EAS
- Email: Resend ($20/month) (NEW)
- **Total Base Cost: $97/month** (NEW)

**Development Environment:**
- Staging: Vercel Preview + Render staging
- Database: Supabase (Free tier)
- CI/CD: GitHub Actions

### Deployment Process

```yaml
# Hub Deployment (NEW)
name: Deploy Hub
on:
  push:
    branches: [main]
    
jobs:
  deploy-hub:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - run: npm test
      - run: npm run build
      - uses: render-deploy-action
      
  deploy-web:
    needs: deploy-hub
    steps:
      - run: vercel deploy --prod
      
  deploy-mobile:
    needs: deploy-hub
    steps:
      - run: eas build --platform all
      - run: eas submit
```

### Monitoring & Alerts

**Hub Monitoring (NEW):**
- Health check endpoint (/health)
- Request/response logging
- Performance metrics
- Error tracking via Sentry

**Business Metrics (NEW):**
- Parsing success rates by tier
- Affiliate conversion tracking
- Viral coefficient monitoring
- Revenue dashboards

**System Monitoring:**
- Uptime monitoring (5-minute checks)
- Error rate alerts (>1% threshold)
- Performance degradation alerts
- API rate limit warnings
- Database connection alerts
- Payment failure notifications

---

## Timeline & Milestones

### Development Phases (Updated for Hub Architecture)

**Phase 1: Foundation & Hub Setup (Weeks 1-3)**
- Project setup and architecture
- Hub infrastructure on Render (NEW)
- Authentication system via hub (NEW)
- Basic trip CRUD operations
- Database schema implementation

**Phase 2: Core Features (Weeks 4-8)**
- Three-tier AI import system (NEW)
- Timeline and Mapbox views (NEW)
- Activity management
- Viral sharing system (ENHANCED)
- Mobile app foundation

**Phase 3: Advanced Features & Monetization (Weeks 9-11)**
- Dual-provider flight integration (NEW)
- Smart caching system (NEW)
- Price tracking with background jobs (NEW)
- Offline sync with Mapbox (NEW)
- Stripe payment integration

**Phase 4: Polish & Launch (Weeks 12-14)**
- Performance optimization
- Security audit
- Beta testing
- Production deployment
- Monitoring setup (NEW)

### Success Metrics

**Launch Metrics:**
- 500 beta users
- <2 second load time
- 99.9% hub uptime (NEW)
- <0.1% error rate
- 95%+ parsing accuracy (NEW)

**6-Month Targets:**
- 10,000 registered users
- 20% paid conversion (NEW: revised from 15%)
- 30% monthly active users
- 4.5+ app store rating
- $8,649+ MRR combined revenue (NEW)
- 0.5+ viral coefficient (NEW)

---

## Risk Mitigation

### Technical Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Hub becomes bottleneck | High | Medium | Horizontal scaling, caching layers, load balancing |
| AI parsing costs exceed budget | High | Medium | Three-tier system, aggressive caching, cost monitoring |
| Dual flight API complexity | Medium | Low | Clear provider routing, fallback mechanisms |
| Mapbox costs escalate | Medium | Low | Usage monitoring, offline optimization |
| Complex itineraries fail parsing | High | Low | Manual correction UI, continuous training |
| Offline sync conflicts | Medium | High | Version control, clear conflict UI |

### Business Risks

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Low affiliate conversions | High | Medium | A/B test flows, optimize provider selection |
| Poor viral growth | High | Medium | Improve sharing UX, incentives, templates |
| Competition copies features | Medium | High | Build community, move faster, focus on UX |
| Low free-to-paid conversion | High | Medium | Test pricing, clear value prop, usage limits |
| Flight API coverage gaps | Low | Low | Dual-provider strategy already mitigates |

### Hub-Specific Risks (NEW)

| Risk | Impact | Probability | Mitigation |
|------|--------|-------------|------------|
| Single point of failure | High | Low | Health monitoring, auto-restart, redundancy |
| Deployment complexity | Low | Low | Blue-green deployments, rollback capability |
| Debugging difficulties | Medium | Low | Centralized logging, distributed tracing |

---

## Success Criteria

### Launch Criteria
- [ ] 95%+ import success rate with three-tier system
- [ ] <2 second page load times
- [ ] <5 second parsing time via hub
- [ ] 99.9% hub uptime
- [ ] Dual flight providers integrated
- [ ] Mapbox offline working
- [ ] Mobile app store approval
- [ ] $10 rule implemented correctly

### 6-Month Success Metrics
- [ ] 10,000+ registered users
- [ ] 20%+ monthly active users
- [ ] $5,000-15,000 monthly affiliate revenue
- [ ] 15%+ paid conversion rate
- [ ] 0.5+ viral coefficient
- [ ] 4.5+ app store rating
- [ ] <$0.05 average parsing cost
- [ ] $8,649+ total MRR

---

## Appendices

### Appendix A: Glossary

- **Hub**: Centralized Node.js/Express service handling all business logic
- **Three-tier parsing**: Regex → Local LLM → OpenRouter API cost optimization
- **Dual-provider**: Duffel (US) + Travelpayouts (International) flight coverage
- **$10 rule**: Show affiliate links only within $10 of lowest price
- **Viral coefficient**: Average number of new users each user brings
- **MRR**: Monthly Recurring Revenue
- **TTL**: Time to Live (cache expiration)

### Appendix B: References

- IEEE 29148:2018 Requirements Engineering
- WCAG 2.1 Accessibility Guidelines
- GDPR Compliance Framework
- OAuth 2.0 Specification
- Mapbox Documentation
- Duffel API Documentation
- Travelpayouts API Documentation

### Appendix C: Architecture Benefits

**Hub Architecture Advantages:**
- Single source of truth for business logic
- Easier debugging and monitoring
- Consistent security implementation
- Rapid development and deployment
- Cost-effective scaling

**Dual-Provider Flight Benefits:**
- 95%+ global route coverage
- Optimal commission rates by region
- Redundancy and fallback options
- Better user experience with comprehensive results

### Appendix D: Approval Sign-offs

| Role | Name | Signature | Date |
|------|------|-----------|------|
| Product Owner | | | |
| Tech Lead | | | |
| QA Lead | | | |
| Security Lead | | | |

---

*This document is version controlled and requires approval for modifications. Submit change requests through the standard RFC process. Updated to incorporate hub architecture, dual-provider strategy, and enhanced features from PRD v2.1.*